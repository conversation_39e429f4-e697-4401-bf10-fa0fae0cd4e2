package com.facishare.crm.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.prm.platform.infrastructure.retry.RetryerFactory;
import com.facishare.crm.task.sfa.common.constants.RebatePolicyConstants;
import com.facishare.crm.task.sfa.model.JobResult;
import com.facishare.crm.task.sfa.model.RebateManualCreateMessage;
import com.facishare.crm.task.sfa.model.rebate.RebatePolicy;
import com.facishare.crm.task.sfa.services.model.OrderUniformityMessage;
import com.facishare.crm.task.sfa.services.model.RebatePolicySourceModel;
import com.facishare.crm.task.sfa.services.qywx.EnterpriseWechatEmployeeService;
import com.facishare.crm.task.sfa.services.rebate.RebateCreateServiceImpl;
import com.facishare.crm.task.sfa.services.rebate.RebateManualCreateServiceImpl;
import com.facishare.crm.task.sfa.services.rebate.RebateSourceConfigService;
import com.facishare.crm.task.sfa.services.rebate.dao.RebatePolicyDao;
import com.facishare.crm.task.sfa.services.rebate.dto.RebateCreateExecParam;
import com.facishare.crm.task.sfa.services.validate.manager.OrderValidationChainServiceManager;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 仅供返利单产生测试
 *
 * <AUTHOR>
 * @date 2023/03/13
 */
@Slf4j
@RestController
@RequestMapping("test")
public class TestController {
    @Autowired
    RebateSourceConfigService rebateSourceConfigService;
    @Autowired
    RebatePolicyDao policyDao;
    @Autowired
    RebateCreateServiceImpl rebateCreateService;
    @Autowired
    EnterpriseWechatEmployeeService enterpriseWechatEmployeeService;
    @Autowired
    private OrderValidationChainServiceManager orderValidationChainServiceManager;
    @Autowired
    RebateManualCreateServiceImpl rebateManualCreateService;
    @Resource
    private RetryerFactory retryerFactory;

    @RequestMapping(value = "/testRebateValidation", method = RequestMethod.POST)
    public JobResult testRebateValidation(@RequestBody String body) throws ParseException {
        OrderUniformityMessage messageBody = JSON.parseObject(body, OrderUniformityMessage.class);
        orderValidationChainServiceManager.getService(messageBody.getType(), messageBody.getActionCode()).ifPresent(service -> {
            service.execute(messageBody);
        });
        return JobResult.builder()
                .success(true)
                .remove(true)
                .build();
    }

    @RequestMapping(value = "/testCreate", method = RequestMethod.POST)
    @ResponseBody
    public JobResult test(@RequestBody String body) throws ParseException {
        JSONObject jsonObject = JSON.parseObject(body);
        String tenantId = jsonObject.getString("tenantId");
        String policyId = jsonObject.getString("policyId");
        String execDateStr = jsonObject.getString("execDate");
        List<RebatePolicy> data = policyDao.getPolicy(tenantId, policyId, null);
        SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date execDate = df1.parse(execDateStr);
        if (StringUtils.isNotBlank(policyId)) {
            String objectId = jsonObject.getString("objectId");
            if (StringUtils.isNotBlank(objectId)) {
                Map<String, RebatePolicySourceModel> apiNameToModel = rebateSourceConfigService.getRebateSourceConfig(tenantId);
                RebatePolicySourceModel rebatePolicySourceModel = apiNameToModel.get("SalesOrderObj");
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, "_id", objectId);
                IObjectData order = policyDao.getBaseData(filters, tenantId, "SalesOrderObj").get(0);
                RebateCreateExecParam param1 = RebateCreateExecParam.builder()
                        .accountId(order.get(rebatePolicySourceModel.getAccountFiled(), String.class))
                        .order(order)
                        .policy(data.get(0))
                        //.orderDetail(orderDetail)
                        .user(User.systemUser(tenantId))
                        .rebatePolicySourceModel(rebatePolicySourceModel)
                        .triggerMode(RebatePolicyConstants.TriggerMode.MANUAL.getValue()).execDate(execDate).build();
                rebateCreateService.execPolicy(param1);
            } else {
                rebateCreateService.createByTenantId(tenantId, execDate, RebatePolicyConstants.TriggerMode.MANUAL.getValue());
            }
        } else {
            Map<String, RebatePolicySourceModel> apiModel = rebateSourceConfigService.getRebateSourceConfig(tenantId);
            rebateCreateService.execAllPolicy(data, execDate, RebatePolicyConstants.TriggerMode.MANUAL.getValue(), apiModel);
        }
        return JobResult.builder()
                .success(true)
                .remove(true)
                .build();
    }

    @RequestMapping(value = "/expectedRebate", method = RequestMethod.POST)
    @ResponseBody
    public JobResult expectedRebate(@RequestBody RebateManualCreateMessage arg) {
        rebateManualCreateService.expectedRebate(arg);
        return JobResult.builder()
                .success(true)
                .remove(true)
                .build();
    }

    @RequestMapping(value = "/retryCallApi", method = RequestMethod.POST)
    @ResponseBody
    public JobResult RetryCallApi() {
        AtomicInteger count = new AtomicInteger(0);

        String result = retryerFactory.<String>create()
                .maxAttempts(5)
                .retryOn(IOException.class)
                .onRetry(e -> System.out.println("Retrying due to: " + e.getMessage()))
                .execute(new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        if (count.incrementAndGet() < 3) {
                            throw new IOException("API 调用失败");
                        }
                        return "成功";
                    }
                })
                .get();
        System.out.println(result);


        AtomicInteger count2 = new AtomicInteger(0);

        retryerFactory.create()
                .maxAttempts(3)
                .retryOn(IOException.class)
                .onRetry(e -> System.out.println("重试中: " + e.getMessage()))
                .execute(() -> {
                    count2.incrementAndGet();
                    throw new IOException("一直失败");
                })
                .get();


        AtomicInteger count3 = new AtomicInteger(0);

        try {
            retryerFactory.create()
                    .maxAttempts(3)
                    .retryOn(IOException.class)
                    .execute(() -> {
                        count3.incrementAndGet();
                        throw new IllegalArgumentException("不会重试这个异常");
                    })
                    .get();
        } catch (Exception e) {
            System.out.println(count3.get());
            System.out.println("不会重试这个异常" + e.getMessage());
        }

        return JobResult.builder()
                .success(true)
                .remove(true)
                .build();
    }

}
