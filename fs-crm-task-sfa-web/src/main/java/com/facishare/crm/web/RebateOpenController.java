package com.facishare.crm.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.task.sfa.model.CustomerAccountModel;
import com.facishare.crm.task.sfa.model.RebateResponse;
import com.facishare.crm.task.sfa.services.AccessIncomeAuthService;
import com.facishare.crm.task.sfa.services.AccessOutcomeAuthService;
import com.facishare.crm.task.sfa.services.AddFundAccountService;
import com.facishare.crm.task.sfa.services.OpenRebateService;
import com.facishare.crm.task.sfa.services.rebate.RebateCreateConfigService;
import com.facishare.crm.task.sfa.services.rebate.dao.mongo.RebateTaskInitDao;
import com.facishare.crm.task.sfa.services.rebate.dao.mongo.RebateTaskInitDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * 仅供返利单产生测试
 *
 * <AUTHOR>
 * @date 2023/03/13
 */
@Slf4j
@RestController
@RequestMapping("rebate")
public class RebateOpenController {
    @Autowired
    OpenRebateService rebateService;
    @Autowired
    AddFundAccountService addFundAccountService;
    @Autowired
    AccessIncomeAuthService initRebateFundAccountService;
    @Autowired
    AccessOutcomeAuthService accessOutcomeAuthService;
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    protected RebateTaskInitDao rebateTaskInitDao;
    @Autowired
    protected RebateCreateConfigService rebateCreateConfigService;

    @RequestMapping(value = "/open", method = RequestMethod.POST)
    @ResponseBody
    public RebateResponse test(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        String tenantId = jsonObject.getString("tenantId");
        //获取userId
        String userId = jsonObject.getString("userId");
        //调用rebateService
        rebateService.openRebate(tenantId, userId);
        return RebateResponse.builder()
                .success(true)
                .build();
    }

    /**
     * 创建和授权客户帐户
     * 历史企业用需要调用此接口
     *
     * @param body 身体
     * @return {@code RebateResponse}
     */
    @RequestMapping(value = "/createAndAuthorizeCustomerAccount", method = RequestMethod.POST)
    @ResponseBody
    public RebateResponse createAndAuthorizeCustomerAccount(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        String tenantId = jsonObject.getString("tenantId");
        //调用rebateService
        try {
            addFundAccountService.createAndAuthorizeCustomerAccount(tenantId);
        } catch (Exception e) {
            log.error("创建返利账户失败,{}", e.getMessage());
            return RebateResponse.builder()
                    .success(false)
                    .errorMsg(e.getMessage())
                    .build();
        }
        return RebateResponse.builder()
                .success(true)
                .build();
    }

    @RequestMapping(value = "/createCustomerAccount", method = RequestMethod.POST)
    @ResponseBody
    public RebateResponse createCustomerAccount(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        String tenantId = jsonObject.getString("tenantId");
        String name = jsonObject.getString("name");
        String accountNo = jsonObject.getString("account_no");
        String accountType = jsonObject.getString("account_type");
        try {
            addFundAccountService.createCustomerAccount(tenantId, name, accountNo, accountType);
        } catch (Exception e) {
            log.error("创建返利账户失败,{}", e.getMessage());
            return RebateResponse.builder()
                    .success(false)
                    .errorMsg(e.getMessage())
                    .build();
        }
        return RebateResponse.builder()
                .success(true)
                .build();
    }


    @RequestMapping(value = "/accessIncomeCustomerAccount", method = RequestMethod.POST)
    @ResponseBody
    public RebateResponse accessIncomeCustomerAccount(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        //从jsonObject 获取 fundAccountIds 转成List<String>
        List<String> fundAccountIds = jsonObject.getObject("fundAccountIds", new TypeReference<List<String>>() {
        });
        String tenantId = jsonObject.getString("tenantId");
        String objectApiName = jsonObject.getString("objectApiName");
        String accountIdApiName = jsonObject.getString("accountIdApiName");
        String incomeAmountApiName = jsonObject.getString("incomeAmountApiName");
        try {
            initRebateFundAccountService.accessFundAccountByIdList(fundAccountIds, tenantId, objectApiName, accountIdApiName, incomeAmountApiName);
        } catch (Exception e) {
            return RebateResponse.builder()
                    .success(false)
                    .errorMsg(e.getMessage())
                    .build();
        }
        return RebateResponse.builder()
                .success(true)
                .build();
    }

    @RequestMapping(value = "/accessOutcomeCustomerAccount", method = RequestMethod.POST)
    @ResponseBody
    public RebateResponse accessOutcomeCustomerAccount(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        //从jsonObject 获取 fundAccountIds 转成List<String>
        List<CustomerAccountModel.FundAccountData> fundAccountIds = new ArrayList<>();
        JSONArray ids = jsonObject.getJSONArray("fundAccountIds");
        for (Object idObj : ids) {
            JSONObject id = (JSONObject) idObj;
            CustomerAccountModel.FundAccountData data = CustomerAccountModel.FundAccountData.builder().accountNo(id.getString("accountNo")).id(id.getString("id")).build();
            fundAccountIds.add(data);
        }
//        List<CustomerAccountModel.FundAccountData> fundAccountIds = jsonObject.getObject("fundAccountIds", new TypeReference<List<CustomerAccountModel.FundAccountData>>() {
//        });
        String tenantId = jsonObject.getString("tenantId");
        String objectApiName = jsonObject.getString("objectApiName");
        String accountIdApiName = jsonObject.getString("accountIdApiName");
        try {
            accessOutcomeAuthService.accessOutcomeAuthByIds(fundAccountIds, tenantId, objectApiName, accountIdApiName);
        } catch (Exception e) {
            return RebateResponse.builder()
                    .success(false)
                    .errorMsg(e.getMessage())
                    .build();
        }
        return RebateResponse.builder()
                .success(true)
                .build();
    }

    @PostMapping("initRebateTask")
    public Object initRebateTask(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        List<String> tenantList = Arrays.asList(jsonObject.getObject("tenantIds", String.class).split(","));
        Set<String> tenantListSet =  rebateCreateConfigService.filterTenantByEnv(tenantList);
        for (String tenantId : tenantListSet) {
            RebateTaskInitDocument taskInitDocument = new RebateTaskInitDocument();
            taskInitDocument.setTenantId(tenantId);
            rebateTaskInitDao.initTask(taskInitDocument);
        }
        return "init success" + JSON.toJSONString(tenantListSet);
    }
}
