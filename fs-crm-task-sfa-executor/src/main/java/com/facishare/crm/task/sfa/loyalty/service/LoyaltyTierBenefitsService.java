package com.facishare.crm.task.sfa.loyalty.service;

import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyI18nKey;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.task.sfa.common.util.CRMRecordUtil;
import com.facishare.crm.task.sfa.loyalty.model.TaskLoyalty;
import com.facishare.crm.task.sfa.model.IssuedCouponInstanceModel;
import com.facishare.crm.task.sfa.services.loyalty.IssuedCouponInstanceService;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LoyaltyTierBenefitsService {

    @Resource
    ServiceFacade serviceFacade;
    @Resource
    IObjectDescribeService iObjectDescribeService;
    @Resource
    CRMNotificationService crmNotificationService;
    @Resource
    IssuedCouponInstanceService issuedCouponInstanceService;

    public static String API_NAME = "LoyaltyTierBenefitsObj";
    /**
     * 权益类型:
     * <p>
     * sys_coupon 优惠卷
     */
    public static String BENEFITS_TYPE = "benefits_type";
    public static String COUPON = "coupon";
    public static String COUPON_AMOUNT = "coupon_amount";
    public static String TIER_ID = "tier_id";

    /**
     * 发放优惠券
     */
    public void couponHandler(String tenantId, String memberId, IObjectData benefit, IObjectData afterTier) {
        if (!"sys_coupon".equals(benefit.get(BENEFITS_TYPE, String.class))) {
            return;
        }
        String couponPlanId = benefit.get(COUPON, String.class);
        Integer couponAmount = benefit.get(COUPON_AMOUNT, Integer.class);
        List<Integer> receiverIds = benefit.getOwner().stream().map(Integer::parseInt).collect(Collectors.toList());
        if (StringUtils.isEmpty(couponPlanId) || couponAmount == null || couponAmount <= 0) {
            return;
        }
        try {
            IssuedCouponInstanceModel issuedCouponInstanceModel = IssuedCouponInstanceModel.builder()
                    .tenantId(tenantId)
                    .memberId(memberId)
                    .couponPlanId(couponPlanId)
                    .total(couponAmount)
                    .build();
            Optional<Map<String, String>> result = issuedCouponInstanceService.issued(issuedCouponInstanceModel);
            if (result.isPresent()) {
                log.error("发放优惠卷失败:{}", result.get());
                sendFailedRecord(tenantId, memberId, receiverIds, afterTier.getName(), benefit.getName());
            }
        } catch (Exception e) {
            log.error("发放优惠卷失败:", e);
            sendFailedRecord(tenantId, memberId, receiverIds, afterTier.getName(), benefit.getName());
        }
    }

    private void sendFailedRecord(String tenantId, String memberId, List<Integer> receiverIds, String afterTierName, String benefitName) {
        CRMRecordUtil.sendNewCRMRecord(
                crmNotificationService,
                User.systemUser(tenantId),
                NewCrmNotification.CUSTOM_REMIND_RECORD_TYPE,
                receiverIds,
                User.SUPPER_ADMIN_USER_ID,
                "升级发放优惠卷失败",
                "会员升级为[{0}]发放优惠卷[{1}]失败",
                LoyaltyI18nKey.UPGRADE_COUPONS_ERROR_TITLE,
                null,
                LoyaltyI18nKey.UPGRADE_COUPONS_ERROR_INFO,
                Lists.newArrayList(afterTierName, benefitName),
                CRMRecordUtil.getUrlParameter(LoyaltyConstants.LoyaltyMember.API_NAME, memberId)
        );
    }

    /**
     * 只查询积分倍率
     */
    public IObjectData findPointsMagnification(String tenantId, String memberId) {
        IObjectData member = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), memberId, LoyaltyConstants.LoyaltyMember.API_NAME);
        if (member == null) {
            return null;
        }
        String tierId = member.get(LoyaltyConstants.LoyaltyMember.TIER_ID, String.class);
        if (StringUtils.isEmpty(tierId)) {
            return null;
        }
        if (nonLoyaltyTierBenefits(tenantId)) {
            return null;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.CREATE_TIME, true)));
        SearchUtil.fillFilterEq(query.getFilters(), TIER_ID, tierId);
        SearchUtil.fillFilterEq(query.getFilters(), BENEFITS_TYPE, "sys_points_magnification");
        QueryResult<IObjectData> result = serviceFacade.findBySearchQueryIgnoreAll(User.systemUser(tenantId), API_NAME, query);
        if (CollectionUtils.isEmpty(result.getData())) {
            return null;
        }
        return result.getData().get(0);
    }

    public List<IObjectData> findTierBenefits(TaskLoyalty.TierBenefitsParam param) {
        String tenantId = param.getTenantId();
        String tierId = param.getTierId();
        if (StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(tierId)) {
            return Lists.newArrayList();
        }
        if (nonLoyaltyTierBenefits(tenantId)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(LoyaltyThreshold.getCommonMaxSize());
        SearchUtil.fillFilterEq(query.getFilters(), TIER_ID, tierId);
        if (!StringUtils.isEmpty(param.getBenefitsType())) {
            SearchUtil.fillFilterEq(query.getFilters(), BENEFITS_TYPE, param.getBenefitsType());
        }
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(User.systemUser(tenantId), API_NAME, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    private boolean nonLoyaltyTierBenefits(String tenantId) {
        try {
            return iObjectDescribeService.findByTenantIdAndDescribeApiName(tenantId, API_NAME) == null;
        } catch (Exception e) {
            log.error("查询描述报错", e);
            return true;
        }
    }

}
