package com.facishare.crm.task.sfa.objchange.service.handler;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.utils.Safes;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.prm.platform.utils.TraceGenerator;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.common.gray.GrayUtils;
import com.facishare.crm.task.sfa.objchange.model.ObjectChangeMessage;
import com.facishare.crm.task.sfa.services.MetadataServiceExt;
import com.facishare.crm.task.sfa.util.ObjectChangeUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-07-10
 * ============================================================
 */
@Component
@Slf4j
public class ProductCategoryChangeHandler implements ObjectChangeHandler {
    @Resource
    private MetadataServiceExt metadataServiceExt;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;

    @Override
    public String getApiName() {
        return "ProductCategoryObj";
    }

    @Override
    public void doService(ObjectChangeMessage msg, MessageExt messageExt) {
        TraceGenerator.generator();
        if (!ObjectChangeUtil.IncludeOperate(msg, Lists.newArrayList("u", "i"))) {
            return;
        }
        String tenantId = msg.getTenantId();
        User user = new User(tenantId, "-10000");
        List<ObjectChangeMessage.Content> changeContent = msg.getBody();
        changeContent.forEach(content -> updateHasChildren(user, content));
    }

    private void updateHasChildren(User user, ObjectChangeMessage.Content content) {
        if (GrayUtils.skipUpdateProductCategoryHasChildrenTenant(user.getTenantId())) {
            log.debug("Skip update product category has children tenant:{}", user.getTenantId());
            return;
        }
        log.info("Update product category has children tenant:{}, content:{}", user.getTenantId(), content);
        // 限速
        sfaTaskRateLimiterService.getUpdateCategoryHasChildrenLimiter().acquire();
        String productCategoryDataId = content.getObjectId();
        JSONObject afterTriggerData = content.getAfterTriggerData();
        JSONObject beforeTriggerData = content.getBeforeTriggerData();
        if (afterTriggerData == null || beforeTriggerData == null) {
            if ("i".equals(content.getTriggerType())) {
                updateHasChildrenWhenCreate(user, content);
            } else {
                log.info("ProductCategoryChangeHandler#updateHasChildren but afterTriggerData or beforeTriggerData is null, tenant:{}, productCategoryDataId:{}", user.getTenantId(), productCategoryDataId);
            }
            return;
        }
        String afterPid = Optional.ofNullable(afterTriggerData.get("pid")).orElse("").toString();
        String beforePid = Optional.ofNullable(beforeTriggerData.get("pid")).orElse("").toString();
        if (StringUtils.isBlank(afterPid)) {
            // 更新后为空，判断之前的父节点还有没有子类，当前节点不处理。
            if (StringUtils.isBlank(beforePid)) {
                return;
            }
            boolean hasChildren = hasChildren(user, beforePid);
            updateHasChildrenField(user, beforePid, hasChildren);
        } else {
            // 更新后不为空，新父节点一定 hasChildren
            updateHasChildrenField(user, afterPid, true);
            if (StringUtils.isBlank(beforePid)) {
                return;
            }
            boolean hasChildren = hasChildren(user, beforePid);
            updateHasChildrenField(user, beforePid, hasChildren);
        }
    }

    private void updateHasChildrenWhenCreate(User user, ObjectChangeMessage.Content content) {
        String newDataId = content.getObjectId();
        if (StringUtils.isBlank(newDataId)) {
            log.warn("ProductCategoryChangeHandler#updateHasChildren but newDataId is null, tenant:{}", user.getTenantId());
            return;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.ID, newDataId);
        List<IObjectData> dataList = metadataServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, "ProductCategoryObj", query, Lists.newArrayList("pid"));
        IObjectData newData = Safes.first(dataList);
        if (newData == null) {
            log.warn("ProductCategoryChangeHandler#updateHasChildren but newData is null, tenant:{}, newDataId:{}", user.getTenantId(), newDataId);
            return;
        }
        String pid = DataUtils.getValue(newData, "pid", String.class);
        if (StringUtils.isBlank(pid)) {
            return;
        }
        updateHasChildrenField(user, pid, true);
    }

    private void updateHasChildrenField(User user, String dataId, boolean newHasChildren) {
        IObjectData productCategoryData = metadataServiceExt.findObjectByIdIgnoreAll(user, dataId, "ProductCategoryObj");
        if (productCategoryData == null) {
            log.warn("ProductCategoryChangeHandler#updateHasChildren updateHasChildrenField but  productCategoryData is null, tenant:{}, dataId:{}", user.getTenantId(), dataId);
            return;
        }
        Boolean originalHasChildren = DataUtils.getValue(productCategoryData, "has_children", Boolean.class);
        if (originalHasChildren != null && originalHasChildren == newHasChildren) {
            return;
        }
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put("has_children", newHasChildren);
        serviceFacade.updateWithMap(user, productCategoryData, updateMap);
    }

    private boolean hasChildren(User user, String dataId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), "pid", dataId);
        query.setLimit(1);
        List<IObjectData> dataList = metadataServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, "ProductCategoryObj", query, Lists.newArrayList("_id"));
        return CollectionUtils.isNotEmpty(dataList);
    }
}
