package com.facishare.crm.task.sfa.services.rebate;

import com.facishare.crm.task.sfa.mq.integral.TenantUtils;
import com.facishare.crm.task.sfa.services.rebate.dao.mongo.RebateTaskInitDao;
import com.facishare.crm.task.sfa.services.rebate.dao.mongo.RebateTaskInitDocument;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RebateCreateConfigService {
    @Autowired
    private TenantUtils tenantUtils;
    @Autowired
    protected RebateTaskInitDao rebateTaskInitDao;
    private String tenantIdsStr = "";

    private void init() {
        ConfigFactory.getInstance().getConfig("variables_tenants", config -> {
            tenantIdsStr = config.get("autoRebateCreateTenantIds");
        });
    }

    public Set<String> getTenants() {
        if (StringUtils.isBlank(tenantIdsStr)) {
            // 读取配置的租户信息
            init();
            if (StringUtils.isBlank(tenantIdsStr)) {
                log.warn("RebateCreateConfigService#getTenants exec break, no tenant need chargeOff,check config variables_tenants autoRebateCreateTenantIds");
                return Sets.newHashSet();
            }
        }

        List<String> tenantList = Arrays.asList(tenantIdsStr.split(","));
        // 去重处理
        Set<String> tenantTemp = new HashSet<>(tenantList);
        List<RebateTaskInitDocument> initDocumentList=rebateTaskInitDao.findAllTasks();
        if(CollectionUtils.notEmpty(initDocumentList)){
            for(RebateTaskInitDocument initDocument:initDocumentList){
                tenantTemp.add(initDocument.getTenantId());
            }
        }

        tenantList  = tenantTemp.stream().distinct().collect(Collectors.toList());

        // 只获取当前云租户
        return  filterTenantByEnv(tenantList);
    }

    public Set<String> filterTenantByEnv(List<String> tenantList) {
        Set<String> tenantListSet = new HashSet<>();
        for (int i = 0; i <= tenantList.size() / 1000; i++) {
            int start = i * 1000;
            int end = Math.min((start + 1000), tenantList.size());
            // 去掉非当前云的租户
            List<String> tempTenant = tenantUtils.batchGetSimpleEnterprise(tenantList.subList(start, end));
            if (CollectionUtils.notEmpty(tempTenant)) {
                tenantListSet.addAll(tempTenant);
            }
        }
        return tenantListSet;
    }

    public String getFirstTenant() {
        if (StringUtils.isBlank(tenantIdsStr)) {
            // 读取配置的租户信息
            init();
            if (StringUtils.isBlank(tenantIdsStr)) {
                log.warn("RebateCreateConfigService#getFirstTenant exec break, no tenant need chargeOff,check config variables_tenants autoRebateCreateTenantIds");
                return null;
            }
        }
        List<String> tenantList = Arrays.asList(tenantIdsStr.split(","));
        return tenantList.get(0);
    }
}
