package com.facishare.crm.task.sfa.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.crm.task.sfa.common.gray.GrayUtils;
import com.facishare.crm.task.sfa.enums.SmsTypeEnums;
import com.facishare.crm.task.sfa.model.PartnerManagement;
import com.facishare.crm.task.sfa.model.PrmManagementModel;
import com.facishare.crm.task.sfa.rest.PartnerChannelRestService;
import com.facishare.crm.task.sfa.services.model.PrmWeChatBindRelationshipModel;
import com.facishare.crm.task.sfa.util.ObjectDataUtils;
import com.facishare.crm.task.sfa.util.PrmConfigUtil;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.enterpriserelation2.arg.ListTenantGroupDownstreamEnterprisesArg;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.mq.event.DownstreamOuterUserData;
import com.fxiaoke.enterpriserelation2.result.ListTenantGroupDownstreamEnterprisesResult;
import com.fxiaoke.enterpriserelation2.service.TenantGroupService;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.services.model.PrmWeChatBindRelationshipModel.*;

/**
 * <AUTHOR>
 * @time 2023-12-07 23:24
 * @Description
 */
@Service
@Slf4j
public class PrmNoticeService {

    @Autowired
    private PartnerChannelRestService partnerChannelRestService;
    @Autowired
    private PrmService prmService;
    @Autowired
    private MetadataServiceExt metadataServiceExt;
    @Autowired
    private TenantGroupService tenantGroupService;
    @Autowired
    PrmWeComService prmWeComService;
    @Resource
    private ErLinkNotificationProxy erLinkNotificationProxy;

    public PrmManagementModel.NoticeConfigResult getPrmNoticeConfig(String tenantId) {
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        return partnerChannelRestService.getPrmNoticeConfig(user);
    }

    public void sendPrmNoticeOfSms(String tenantId, Map<String, List<String>> tenantUserMapping, PrmManagementModel.NoticeConfig noticeConfig) {
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        String sendSmsUserId = StringUtils.isBlank(noticeConfig.getOwner()) ? "1000" : noticeConfig.getOwner();
        PartnerManagement.SmsContent smsContent = convertSmsContent(noticeConfig);
        // 查询电话号根据 tenantUserMapping
        Set<String> outerUserPhoneNumbers = findOuterUserPhoneNumber(user, tenantUserMapping);
        outerUserPhoneNumbers.forEach(phoneNumber -> prmService.sendSms(user, Integer.parseInt(sendSmsUserId), phoneNumber, smsContent));
    }


    private Set<String> findOuterUserPhoneNumber(User user, Map<String, List<String>> tenantUserMapping) {
        Set<String> phoneNumbers = Sets.newHashSet();
        tenantUserMapping.forEach((outerTenant, outUser) -> {
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(1000);
            SearchUtil.fillFilterEq(query.getFilters(), "outer_tenant_id", outerTenant);
            SearchUtil.fillFilterIn(query.getFilters(), "outer_uid", outUser);
            Set<String> mobileNumbers = metadataServiceExt.findBySearchQueryIgnoreAll(user, "PublicEmployeeObj", query)
                    .stream().filter(data -> StringUtils.isNotBlank(ObjectDataUtils.getValueOrDefault(data, "mobile", "")))
                    .map(data -> data.get("mobile", String.class)).collect(Collectors.toSet());
            phoneNumbers.addAll(mobileNumbers);
        });
        return phoneNumbers;
    }

    private PartnerManagement.SmsContent convertSmsContent(PrmManagementModel.NoticeConfig noticeConfig) {
        PartnerManagement.SmsContent smsContent = PartnerManagement.SmsContent.builder().build();
        PrmManagementModel.ShortMessage shortMessage = noticeConfig.getShortMessage();
        SmsTypeEnums smsType = SmsTypeEnums.getSmsType(shortMessage.getSmsType());
        if (SmsTypeEnums.CUSTOM_SMS.equals(smsType)) {
            String content = shortMessage.getContent();
            smsContent.setContent(content);
        } else {
            String templateId = shortMessage.getTemplateId();
            smsContent.setTemplateId(templateId);
            List<PartnerManagement.ContentParam> smsContentParam = shortMessage.getSmsContentParam();
            smsContent.setSmsContentParam(smsContentParam);
        }
        return smsContent;
    }

    public PrmManagementModel.NoticeRange mergeNoticeRange(PrmManagementModel.NoticeConfig noticeConfig) {
        PrmManagementModel.NoticeRange noticeRange = PrmManagementModel.NoticeRange.builder().build();
        if (noticeConfig.getNoticeRange().getIncludeAllTenant()) {
            noticeRange.setIncludeAllTenant(true);
            return noticeRange;
        }
        noticeRange.setIncludeAllTenant(false);
        noticeRange.setOuterTenants(noticeConfig.getNoticeRange().getOuterTenants());
        noticeRange.setOuterGroup(noticeConfig.getNoticeRange().getOuterGroup());
        return noticeRange;
    }

    public Set<String> getOuterTenantsOfGroup(String tenantId, List<String> outerGroup) {
        if (CollectionUtils.isEmpty(outerGroup)) {
            return Sets.newHashSet();
        }
        Set<String> outerTenants = Sets.newHashSet();
        outerGroup.forEach(groupId -> outerTenants.addAll(getDownstreamTenantsByGroup(tenantId, groupId)));
        return outerTenants;
    }

    private Set<String> getDownstreamTenantsByGroup(String tenantId, String groupId) {
        ListTenantGroupDownstreamEnterprisesArg arg = new ListTenantGroupDownstreamEnterprisesArg();
        arg.setTenantGroupId(groupId);
        RestResult<ListTenantGroupDownstreamEnterprisesResult> downstreamTenantsRst = tenantGroupService.listTenantGroupDownstreamEnterprises(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(Integer.valueOf(tenantId)), arg);
        if (downstreamTenantsRst == null || downstreamTenantsRst.getData() == null) {
            return Sets.newHashSet();
        }
        return Optional.ofNullable(downstreamTenantsRst.getData().getTenantGroupDownstreamEnterpriseVos()).orElse(com.google.common.collect.Lists.newArrayList()).stream()
                .filter(v -> v.getOuterTenantId() != null)
                .map(v -> String.valueOf(v.getOuterTenantId())).collect(Collectors.toSet());
    }

    public void changePrmAppBindingRelationship(String upstreamTenantId, List<DownstreamOuterUserData> outerUserData, boolean isBindApp) {
        if (CollectionUtils.isEmpty(outerUserData)) {
            return;
        }
        PrmManagementModel.NoticeConfigResult prmNoticeConfig = getPrmNoticeConfig(upstreamTenantId);
        // 提醒配置未生效，直接返回
        if (!Boolean.TRUE.equals(prmNoticeConfig.getValid())) {
            return;
        }
        PrmManagementModel.NoticeConfig noticeConfig = prmNoticeConfig.getDeactivateConfig();
        if (isBindApp) {
            noticeConfig = prmNoticeConfig.getActivateConfig();
        }
        PrmManagementModel.NoticeRange noticeRange = mergeNoticeRange(prmNoticeConfig.getActivateConfig());
        Boolean includeAllTenant = noticeRange.getIncludeAllTenant();
        Set<String> rangeTenants = getOuterTenantsOfGroup(upstreamTenantId, noticeRange.getOuterGroup());
        rangeTenants.addAll(noticeRange.getOuterTenants());
        Map<String, List<String>> sendSmsTenantUserMapping = outerUserData.stream()
                .filter(x -> belongRangeTenant(includeAllTenant, rangeTenants, String.valueOf(x.getDownstreamOuterTenantId())))
                .collect(Collectors.groupingBy(t -> String.valueOf(t.getDownstreamOuterTenantId()),
                        Collectors.mapping(u -> String.valueOf(u.getDownstreamOuterUid()), Collectors.toList())));
        sendPrmNoticeOfSms(upstreamTenantId, sendSmsTenantUserMapping, noticeConfig);
    }

    private boolean belongRangeTenant(Boolean includeAllTenant, Set<String> rangeTenants, String downstreamOuterTenantId) {
        return includeAllTenant || rangeTenants.contains(downstreamOuterTenantId);
    }

    public void forwardPrmTextMsg2WeCom(MessageExt messageExt) {
        SendTextMessageArg textMessage = JSON.parseObject(messageExt.getBody(), SendTextMessageArg.class);
        String tenantId = String.valueOf(textMessage.getEi());
        if (textMessage.getReceiverChannelType() != 1) {
            return;
        }
        PrmWeChatBindRelationshipModel.CrmMessage crmMessage = convertTextCrmMessage(textMessage);
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        pushMessage(crmMessage, user);

    }

    private void pushMessage(CrmMessage crmMessage, User user) {
        if (!GrayUtils.isVersionGrayTenant(user.getTenantId())) {
            return;
        }
        if (!GrayUtils.isSupportPrmWeChatAppTenant(user.getTenantId())) {
            return;
        }
        Set<Long> receiverIds = crmMessage.getReceiverIds().stream().filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toSet());
        erLinkNotificationProxy.pushNotification(user, receiverIds, crmMessage.getContent());
    }

    private PrmWeChatBindRelationshipModel.CrmMessage convertTextCrmMessage(SendTextMessageArg textMessage) {
        PrmWeChatBindRelationshipModel.CrmMessage crmMessage = new PrmWeChatBindRelationshipModel.CrmMessage();
        crmMessage.setTitle(I18N.text(PRM_MESSAGE_NOTICE_TITLE));
        crmMessage.setContent(textMessage.getMessageContent());
        crmMessage.setReceiverIds(textMessage.getReceiverIds());
        Map<String, String> extraDataMap = textMessage.getExtraDataMap();
        if (extraDataMap != null && !extraDataMap.isEmpty()) {
            String outUser = extraDataMap.get(RECEIVER_IDS_ARG_SEND);
            if (StringUtils.isNotBlank(outUser)) {
                crmMessage.getReceiverIds().add(Integer.valueOf(outUser));
            }
        }
        return crmMessage;
    }

    private PrmWeChatBindRelationshipModel.WeComMessage convertWeComMessage(User user, PrmWeChatBindRelationshipModel.CrmMessage crmMessage) {
        List<Integer> receiverIds = crmMessage.getReceiverIds();
        if (CollectionUtils.isEmpty(receiverIds)) {
            log.warn("代理通消息转企信：receiverIds 是空的。tenant:{}", user.getTenantId());
            return null;
        }
        List<Integer> filterReceiverIds = receiverIds.stream().filter(i -> i != null && i > 100000000).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterReceiverIds)) {
            log.warn("代理通消息转企信：filterReceiverIds 是空的。tenant:{}", user.getTenantId());
            return null;
        }
        PrmWeChatBindRelationshipModel.PrmWeComBindConfig prmWeComBindConfig = prmWeComService.findPrmWeComBindConfig(user);
        if (prmWeComBindConfig == null) {
            log.warn("代理通消息转企信：prmWeComBindConfig 是空的。tenant:{}", user.getTenantId());
            return null;
        }
        log.info("代理通消息转企信：findPrmWeComBindRelationships 是空的。tenant:{}, prmWeComBindConfig:{}", user.getTenantId(), prmWeComBindConfig);
        List<IObjectData> prmWeComBindRelationships = prmWeComService.findPrmWeComBindRelationships(user, filterReceiverIds, prmWeComBindConfig.getWeComCorpId(), prmWeComBindConfig.getWeComPrmAppId());
        if (CollectionUtils.isEmpty(prmWeComBindRelationships)) {
            log.warn("代理通消息转企信：prmWeComBindRelationships 是空的。tenant:{}", user.getTenantId());
            return null;
        }
        PrmWeChatBindRelationshipModel.WeComMessage weComMessage = new PrmWeChatBindRelationshipModel.WeComMessage();
        weComMessage.setCrmMessage(crmMessage);
        weComMessage.setPrmWeComBindConfig(prmWeComBindConfig);
        List<PrmWeChatBindRelationshipModel.PrmWeChatBindRelationship> prmWeChatBindRelationships = convertPrmWeChatBindRelationships(prmWeComBindRelationships);
        weComMessage.setPrmWeChatBindRelationships(prmWeChatBindRelationships);
        return weComMessage;
    }

    private List<PrmWeChatBindRelationshipModel.PrmWeChatBindRelationship> convertPrmWeChatBindRelationships(List<IObjectData> prmWeComBindRelationships) {
        if (CollectionUtils.isEmpty(prmWeComBindRelationships)) {
            return Lists.newArrayList();
        }
        List<PrmWeChatBindRelationshipModel.PrmWeChatBindRelationship> bindRelationships = Lists.newArrayList();
        for (IObjectData data : prmWeComBindRelationships) {
            String weChatAppId = ObjectDataUtils.getValueOrDefault(data, WECHAT_APP_ID, "");
            String weChatCorpId = ObjectDataUtils.getValueOrDefault(data, WECHAT_CORP_ID, "");
            String weChatUserId = ObjectDataUtils.getValueOrDefault(data, WECHAT_USER_ID, "");
            if (StringUtils.isAnyBlank(weChatAppId, weChatCorpId, weChatUserId)) {
                continue;
            }
            PrmWeChatBindRelationshipModel.PrmWeChatBindRelationship bindRelationship = new PrmWeChatBindRelationshipModel.PrmWeChatBindRelationship();
            bindRelationship.setWechatAppId(weChatAppId);
            bindRelationship.setWechatCorpId(weChatCorpId);
            bindRelationship.setWechatUserId(weChatUserId);
            bindRelationships.add(bindRelationship);
        }
        return bindRelationships;
    }

    public void forwardPrmCardMsg2WeCom(MessageExt messageExt) {
        SendTextCardMessageArg textCardMessage = JSON.parseObject(messageExt.getBody(), SendTextCardMessageArg.class);
        String tenantId = String.valueOf(textCardMessage.getEi());
        if (textCardMessage.getReceiverChannelType() != 1) {
            return;
        }
        PrmWeChatBindRelationshipModel.CrmMessage crmMessage = convertTextCardCrmMessage(textCardMessage);
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        pushMessage(crmMessage, user);
    }

    private CrmMessage convertTextCardCrmMessage(SendTextCardMessageArg textCardMessage) {
        PrmWeChatBindRelationshipModel.CrmMessage crmMessage = new PrmWeChatBindRelationshipModel.CrmMessage();
        crmMessage.setTitle(textCardMessage.getTitle());
        crmMessage.setContent(textCardMessage.getMessageContent());
        crmMessage.setReceiverIds(textCardMessage.getReceiverIds());
        List<Integer> extraReceivers = parseReceiverFromExtraData(textCardMessage, crmMessage);
        // Defense NPE
        if (crmMessage.getReceiverIds() == null) {
            crmMessage.setReceiverIds(Lists.newArrayList());
        }
        crmMessage.getReceiverIds().addAll(extraReceivers);
        return crmMessage;
    }

    private List<Integer> parseReceiverFromExtraData(SendTextCardMessageArg textCardMessage, CrmMessage crmMessage) {
        Map<String, String> extraDataMap = textCardMessage.getExtraDataMap();
        if (extraDataMap == null || extraDataMap.isEmpty()) {
            return Lists.newArrayList();
        }
        String outUser = extraDataMap.get(RECEIVER_IDS_ARG_SEND);
        if (StringUtils.isBlank(outUser)) {
            return Lists.newArrayList();
        }
        try {
            return JSONArray.parseObject(outUser, List.class);
        } catch (Exception e) {
            log.warn("parseReceiverFromExtraData error, outUser:{}", outUser, e);
        }
        return Lists.newArrayList();
    }

    public void forwardPrmDealTodo2WeCom(MessageExt messageExt) {
        DealTodoArg dealTodoMessage = JSON.parseObject(messageExt.getBody(), DealTodoArg.class);
        String tenantId = String.valueOf(dealTodoMessage.getEi());
        PrmWeChatBindRelationshipModel.CrmMessage crmMessage = convertDealTodoCrmMessage(dealTodoMessage);
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        pushMessage(crmMessage, user);
    }

    private CrmMessage convertDealTodoCrmMessage(DealTodoArg dealTodoMessage) {
        PrmWeChatBindRelationshipModel.CrmMessage crmMessage = new PrmWeChatBindRelationshipModel.CrmMessage();
        crmMessage.setTitle(I18N.text(PRM_MESSAGE_NOTICE_TITLE));
        crmMessage.setContent(I18N.text(PRM_MESSAGE_NOTICE_DEAL_TODO));
        crmMessage.setReceiverIds(dealTodoMessage.getHandleUserIds());
        return crmMessage;

    }
}
