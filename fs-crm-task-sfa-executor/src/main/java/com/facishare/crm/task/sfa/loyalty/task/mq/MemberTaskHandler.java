package com.facishare.crm.task.sfa.loyalty.task.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.sfa.lto.loyalty.service.memberTierStrategy.IMemberTierStrategy;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.task.sfa.loyalty.model.TaskLoyalty;
import com.facishare.crm.task.sfa.loyalty.service.LoyaltyPointsDetailClearService;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 每晚更新积分状态/检测评定日
 */
@Service
public class MemberTaskHandler extends LoyaltyMqHandler {

    @Resource
    ServiceFacade serviceFacade;
    @Resource(name = "SFAJedisCmd")
    MergeJedisCmd jedisCmd;
    @Resource(name = "loyaltyMq")
    AutoConfMQProducer mqProducer;
    @Resource
    LoyaltyPointsDetailClearService loyaltyPointsDetailClearService;

    @Override
    public Tag getTag() {
        return Tag.tenantMemberTask;
    }

    @Override
    public void handler(String body) {
        TaskLoyalty.CoreTaskInfo coreTaskInfo = JSON.parseObject(body, TaskLoyalty.CoreTaskInfo.class);
        String tenantId = coreTaskInfo.getTenantId();
        if (!enable(tenantId)) {
            return;
        }
        task(coreTaskInfo);
    }

    public void task(TaskLoyalty.CoreTaskInfo coreTaskInfo) {
        String traceId = TraceContext.get().getTraceId();
        String tenantId = coreTaskInfo.getTenantId();
        if (StringUtils.isEmpty(coreTaskInfo.getTraceId())) {
            coreTaskInfo.setTraceId(traceId);
        } else {
            TraceContext.get().setTraceId(coreTaskInfo.getTraceId());
        }
        log.info("会员定时任务-开始.tenantId:[{}],param:{}", tenantId, JSON.toJSONString(coreTaskInfo));
        //更新积分明细 - 状态
        if (0 == coreTaskInfo.getStatus()) {
            //过期
            Set<String> memberIdList = loyaltyPointsDetailClearService.clearPointsDetailForExpired(coreTaskInfo);
            if (CollectionUtils.isEmpty(memberIdList)) {
                coreTaskInfo.setStatus(1);
                mqProducer.send(new DefaultTopicMessage(LoyaltyMqHandler.Tag.tenantMemberTask.name(), JSON.toJSONBytes(coreTaskInfo)));
            } else {
                tempSaveMemberIdList(tenantId, memberIdList);
                mqProducer.send(new DefaultTopicMessage(LoyaltyMqHandler.Tag.tenantMemberTask.name(), JSON.toJSONBytes(coreTaskInfo)));
            }
        } else if (1 == coreTaskInfo.getStatus()) {
            Long currentTime = System.currentTimeMillis();
            //解冻
            List<IFilter> filtersAvailable = new ArrayList<>();
            if (!StringUtils.isEmpty(coreTaskInfo.getPointsDetailAvailableOffset())) {
                SearchUtil.fillFilterGT(filtersAvailable, IObjectData.ID, coreTaskInfo.getPointsDetailAvailableOffset());
            }
            SearchUtil.fillFilterEq(filtersAvailable, LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, "Frozen");
            SearchUtil.fillFilterGT(filtersAvailable, LoyaltyConstants.LoyaltyPointsDetail.EXPIRY_TIME, currentTime);
            SearchUtil.fillFilterLT(filtersAvailable, LoyaltyConstants.LoyaltyPointsDetail.EFFECTIVE_TIME, currentTime);
            Set<String> memberIdList = updateByQuery(tenantId, filtersAvailable, "Available", coreTaskInfo::setPointsDetailAvailableOffset);
            if (CollectionUtils.isEmpty(memberIdList)) {
                coreTaskInfo.setStatus(2);
                mqProducer.send(new DefaultTopicMessage(LoyaltyMqHandler.Tag.tenantMemberTask.name(), JSON.toJSONBytes(coreTaskInfo)));
            } else {
                tempSaveMemberIdList(tenantId, memberIdList);
                mqProducer.send(new DefaultTopicMessage(LoyaltyMqHandler.Tag.tenantMemberTask.name(), JSON.toJSONBytes(coreTaskInfo)));
            }
        } else if (2 == coreTaskInfo.getStatus()) {
            SearchTemplateQuery query = new SearchTemplateQuery();
            if (!StringUtils.isEmpty(coreTaskInfo.getEvaluationDateOffset())) {
                SearchUtil.fillFilterGT(query.getFilters(), IObjectData.ID, coreTaskInfo.getEvaluationDateOffset());
            }
            SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
            SearchUtil.fillFilterLTE(query.getFilters(), LoyaltyConstants.LoyaltyMember.EVALUATION_DATE, System.currentTimeMillis());
            query.setLimit(LoyaltyThreshold.getCommonPageSize());
            query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyMember.API_NAME, query);
            if (queryResult != null && !CollectionUtils.isEmpty(queryResult.getData())) {
                Set<String> tempMemberIds = queryResult.getData().stream().map(IObjectData::getId).collect(Collectors.toSet());
                asyncMemberHandler(tenantId, traceId, tempMemberIds, IMemberTierStrategy.Operator.task);
                removeTempMemberIdList(tenantId, tempMemberIds);
                //传递偏移量
                List<IObjectData> dataList = queryResult.getData();
                IObjectData lastData = dataList.get(dataList.size() - 1);
                coreTaskInfo.setEvaluationDateOffset(lastData.getId());
                mqProducer.send(new DefaultTopicMessage(LoyaltyMqHandler.Tag.tenantMemberTask.name(), JSON.toJSONBytes(coreTaskInfo)));
            } else {
                Set<String> memberIdList = getTempMemberIdList(tenantId);
                asyncMemberHandler(tenantId, traceId, memberIdList, IMemberTierStrategy.Operator.upgrades);
                removeTempMemberIdList(tenantId, memberIdList);
                log.info("会员定时任务-结束时.tenantId:[{}]", tenantId);
            }
        }
    }

    private String getKey(String tenantId) {
        return "sfa_loyalty_task_temp_member_" + tenantId;
    }

    public void tempSaveMemberIdList(String tenantId, Collection<String> memberIdList) {
        if (CollectionUtils.isEmpty(memberIdList)) {
            return;
        }
        jedisCmd.sadd(getKey(tenantId), memberIdList.toArray(new String[0]));
        jedisCmd.expire(getKey(tenantId), 2 * 60 * 60 * 1000L);
    }

    public Set<String> getTempMemberIdList(String tenantId) {
        return jedisCmd.smembers(getKey(tenantId));
    }

    public void removeTempMemberIdList(String tenantId, Collection<String> memberIdList) {
        if (CollectionUtils.isEmpty(memberIdList)) {
            return;
        }
        jedisCmd.srem(getKey(tenantId), memberIdList.toArray(new String[0]));
    }

    private Set<String> updateByQuery(String tenantId, List<IFilter> filters, String status, Consumer<String> consumer) {
        Map<String, Object> updateFields = new HashMap<>();
        updateFields.put(LoyaltyConstants.LoyaltyPointsDetail.POINTS_STATUS, status);

        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), IObjectData.IS_DELETED, 0);
        query.getFilters().addAll(filters);
        query.setLimit(LoyaltyThreshold.getCommonPageSize());
        query.setOrders(Lists.newArrayList(new OrderBy(IObjectData.ID, true)));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), LoyaltyConstants.LoyaltyPointsDetail.API_NAME, query);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return new HashSet<>();
        }
        serviceFacade.batchUpdateWithMap(User.systemUser(tenantId), queryResult.getData(), updateFields);
        List<IObjectData> dataList = queryResult.getData();
        IObjectData lastData = dataList.get(dataList.size() - 1);
        String offset = lastData.getId();
        consumer.accept(offset);
        return dataList.stream().map(e -> e.get(LoyaltyConstants.LoyaltyPointsDetail.MEMBER_ID, String.class)).collect(Collectors.toSet());
    }

    public void asyncMemberHandler(String tenantId, String traceId, Collection<String> memberIdList, IMemberTierStrategy.Operator operator) {
        if (CollectionUtils.isEmpty(memberIdList)) {
            return;
        }
        List<Message> messageList = new ArrayList<>();
        for (String memberId : memberIdList) {
            Loyalty.MemberTask memberTask = new Loyalty.MemberTask();
            memberTask.setTenantId(tenantId);
            memberTask.setTraceId(traceId + "/" + memberId);
            memberTask.setMemberId(memberId);
            memberTask.setOperator(operator.name());
            messageList.add(new Message(mqProducer.getDefaultTopic(), Tag.task.name(), JSON.toJSONBytes(memberTask)));
        }
        mqProducer.send(messageList);
    }
}
