package com.facishare.crm.task.sfa.services.loyalty;

import com.facishare.crm.sfa.audit.log.SFAAuditLogService;
import com.facishare.crm.sfa.audit.log.model.AuditArg;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.common.constants.SystemConstants;
import com.facishare.crm.task.sfa.common.util.CRMRecordUtil;
import com.facishare.crm.task.sfa.model.RuleWhere;
import com.facishare.crm.task.sfa.model.loyalty.IncentivePolicy;
import com.facishare.crm.task.sfa.model.loyalty.IncentivePolicyAction;
import com.facishare.crm.task.sfa.model.loyalty.IncentivePolicyRule;
import com.facishare.crm.task.sfa.services.SFAMessageService;
import com.facishare.crm.task.sfa.services.loyalty.action.ActionServiceManager;
import com.facishare.crm.task.sfa.services.loyalty.dao.ExtendedAttributeDao;
import com.facishare.crm.task.sfa.services.loyalty.dao.IncentiveLogDao;
import com.facishare.crm.task.sfa.services.loyalty.dao.IncentivePolicyDao;
import com.facishare.crm.task.sfa.services.loyalty.dao.TransactionResultDetailDao;
import com.facishare.crm.task.sfa.services.loyalty.dto.LoyaltyActionResult;
import com.facishare.crm.task.sfa.services.loyalty.dto.LoyaltyEventResult;
import com.facishare.crm.task.sfa.services.loyalty.dto.LoyaltyExecParam;
import com.facishare.crm.task.sfa.services.loyalty.i18n.I18NConstants;
import com.facishare.crm.task.sfa.services.loyalty.util.*;
import com.facishare.crm.task.sfa.services.rebate.ExecuteFunctionServiceImpl;
import com.facishare.crm.task.sfa.services.rebate.rest.RuleEngineLogicService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class LoyaltyCalcServiceImpl {
    @Resource
    private IncentivePolicyDao policyDao;
    @Resource
    private IncentiveLogDao policyLogDao;
    @Resource
    private RuleEngineLogicService engineLogicService;
    @Resource
    private ExecuteFunctionServiceImpl functionService;
    @Resource
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;
    @Resource
    private ActionServiceManager actionServiceManager;
    @Resource
    private SFAMessageService messageService;
    @Resource
    protected ServiceFacade serviceFacade;
    @Resource
    private SFAAuditLogService sfaAuditLogService;
    @Resource
    protected ExtendedAttributeDao extendedAttributeDao;
    @Resource
    protected TransactionResultDetailDao detailDao;
    @Autowired
    protected CRMNotificationServiceImpl crmNotificationService;
    @Autowired
    protected InfraServiceFacade infraServiceFacade;
    @Resource
    private IncentiveMetricService incentiveMetricService;

    /**
     * 根据事件进行计算
     *
     * @param userId 执行用户
     * @param event  事件
     * @param retry  是否重试
     */
    public void createByEvent(IObjectData event, String userId, boolean retry) {
        long start = System.currentTimeMillis();
        AuditArg auditArg = this.getEventLogInfo(event);
        auditArg.setExtra(String.valueOf(retry));
        StopWatch stopWatch = StopWatch.createStarted("createByEvent");
        Date execDate = new Date();
        String tenantId = event.getTenantId();
        User user = new User(tenantId, userId);
        RequestContext requestContext = RequestContext.builder().user(user).build();
        RequestContextManager.setContext(requestContext);
        // 判断租户是否已经执行过
        List<IObjectData> eventLogs = policyLogDao.getEventLog(event);
        stopWatch.lap("getEventLog");
        String msg;
        if (hasExecLog(eventLogs) && !retry) {
            log.warn("loyalty event has calc, event id {} , log id {} ", event.getId(), eventLogs.get(0).getId());
            msg = "loyalty event has calc";
            sendAuditLog(auditArg, msg);
            return;
        }
        IObjectData eventLog = policyLogDao.addEventLog(event);
        stopWatch.lap("addEventLog");

        event.set(TransactionEventConstants.TRANSACTION_STATUS, TransactionEventConstants.TransactionStatusType.CALCULATE.getTransactionStatusType());
        policyLogDao.batchUpdateByFieldSkip(user, Lists.newArrayList(event), Lists.newArrayList(TransactionEventConstants.TRANSACTION_STATUS));
        stopWatch.lap("updateEventStatusCalculate");

        // 获取待执行政策
        IncentivePolicy policyInfo = IncentivePolicy.builder()
                .firstCategory(event.get(TransactionEventConstants.FIRST_CATEGORY, String.class))
                .secondCategory(event.get(TransactionEventConstants.SECOND_CATEGORY, String.class))
                .thirdCategory(event.get(TransactionEventConstants.THIRD_CATEGORY, String.class))
                .programId(event.get(TransactionEventConstants.PROGRAM_ID, String.class))
                .storeRange(event.get(TransactionEventConstants.TRANSACTION_STORE_ID, String.class))
                .tenantId(tenantId).build();
        List<IncentivePolicy> policyList = policyDao.getPolicy(policyInfo);
        log.info("事件ID：{}，query policy id is {}", event.getId(), policyList.stream().map(IncentivePolicy::getId).collect(Collectors.toList()));
        stopWatch.lap("getPolicy");
        LoyaltyExecParam param = LoyaltyExecParam.builder()
                .event(event)
                .allPolicy(policyList)
                .user(user)
                .memberId(event.get(TransactionEventConstants.MEMBER_ID, String.class))
                .programId(event.get(TransactionEventConstants.PROGRAM_ID, String.class))
                .retry(retry)
                .eventLogId(eventLog.getId())
                .contextMap(Maps.newHashMap())
                .execDate(execDate).build();
        addLogDescribe(param);
        // 执行政策
        LoyaltyEventResult result = execAllPolicy(param, stopWatch);
        event.set(TransactionEventConstants.TRANSACTION_STATUS, result.getResult().getTransactionStatusType());
        eventLog.set(IncentivePolicyLogConstants.REASON, result.getExtMsg());
        if (StringUtils.isNotBlank(result.getExtMsgKey())) {
            Map<String, String> languageMap = I18NConstants.getLanguageMap(result.getExtMsgKey());
            if (param.isReasonEnableMultiLang() && MapUtils.isNotEmpty(languageMap)) {
                eventLog.set(IncentivePolicyLogConstants.REASON + "__lang", languageMap);
            }
        }
        // 记录运行成功日志
        if (TransactionEventConstants.TransactionStatusType.FINISH.getTransactionStatusType().equals(result.getResult().getTransactionStatusType())) {
            msg = "loyalty event calc success。" + result.getExtMsg();
            eventLog.set(IncentivePolicyLogConstants.EXECUTE_RESULT, IncentivePolicyLogConstants.ExecuteResultType.SUCCESS.getExecuteResultType());
        } else {
            msg = "loyalty event calc fail" + result.getExtMsg();
            eventLog.set(IncentivePolicyLogConstants.EXECUTE_RESULT, IncentivePolicyLogConstants.ExecuteResultType.FAIL.getExecuteResultType());
        }

        policyLogDao.batchUpdateByFieldSkip(user, Lists.newArrayList(eventLog),
                Lists.newArrayList(IncentivePolicyLogConstants.EXECUTE_RESULT, IncentivePolicyLogConstants.REASON, IncentivePolicyLogConstants.REASON + "__lang"));
        stopWatch.lap("updateEventLog");
        policyLogDao.batchUpdateByFieldSkip(user, Lists.newArrayList(event), Lists.newArrayList(TransactionEventConstants.TRANSACTION_STATUS));
        stopWatch.lap("updateEventStatusFinish");
        sendRetryMsg(event, param, user, result);
        stopWatch.lap("sendRetryMsg");
        auditArg.setCost(System.currentTimeMillis() - start);
        auditArg.setMessageId(eventLog.getId());
        sendAuditLog(auditArg, msg);
        stopWatch.lap("sendAuditLog");
        stopWatch.logSlow(500);
    }

    private void addLogDescribe(LoyaltyExecParam param) {
        IObjectDescribe IncentivePolicyLogDescribe = serviceFacade.findObject(param.getUser().getTenantId(), "IncentivePolicyLogObj");
        boolean enableMultiLang = Optional.of(IncentivePolicyLogDescribe)
                .map(x -> x.getFieldDescribe(IncentivePolicyLogConstants.REASON))
                .map(x -> x.get("enable_multi_lang", Boolean.class))
                .filter(BooleanUtils::isTrue).orElse(false);
        param.setIncentivePolicyLogDescribe(IncentivePolicyLogDescribe);
        param.setReasonEnableMultiLang(enableMultiLang);
    }

    private void sendAuditLog(AuditArg auditArg, String msg) {
        auditArg.setMessage(msg);
        sfaAuditLogService.sendAuditLog(auditArg, LoyaltyCalcConstants.SFA_AUDIT_LOG);
    }

    private AuditArg getEventLogInfo(IObjectData event) {
        AuditArg auditArg = new AuditArg();
        auditArg.setBizName("LoyaltyCalc");
        auditArg.setEi(event.getTenantId());
        auditArg.setObjectApiName(event.getDescribeApiName());
        auditArg.setObjectIds(event.getId());
        auditArg.setExtra5("event");
        return auditArg;
    }

    private void sendRetryMsg(IObjectData event, LoyaltyExecParam param, User user, LoyaltyEventResult result) {
        if (param.isRetry()) {
            String msg = "";
            boolean fail = false;
            if (!TransactionEventConstants.TransactionStatusType.FINISH.getTransactionStatusType().equals(result.getResult().getTransactionStatusType())) {
                fail = true;
                msg = result.getExtMsg();
            }
            if (StringUtils.isBlank(msg) && StringUtils.isNotBlank(param.getActionFailMsg())) {
                msg = param.getActionFailMsg();
            }
            sendMsg(event, user, LoyaltyCalcConstants.ManualType.RETRY, fail, msg);
        }
    }

    @NotNull
    private TransactionEventConstants.TransactionStatusType getTransactionStatusType(LoyaltyExecParam param) {
        TransactionEventConstants.TransactionStatusType status = TransactionEventConstants.TransactionStatusType.FINISH;
        if (param.isHasFailAction()) {
            status = TransactionEventConstants.TransactionStatusType.ERROR;
        }
        return status;
    }

    private boolean hasExecLog(List<IObjectData> eventLogs) {
        return CollectionUtils.notEmpty(eventLogs)
                && eventLogs.stream()
                // 过滤掉执行失败的日志和部分成功的日志
                .anyMatch(log -> !(IncentivePolicyLogConstants.ExecuteResultType.FAIL.getExecuteResultType().equals(log.get(IncentivePolicyLogConstants.EXECUTE_RESULT, String.class)) ||
                        IncentivePolicyLogConstants.ExecuteResultType.PART.getExecuteResultType().equals(log.get(IncentivePolicyLogConstants.EXECUTE_RESULT, String.class))));

    }

    public LoyaltyEventResult execAllPolicy(LoyaltyExecParam param, StopWatch stopWatch) {
        List<IncentivePolicy> policyList = param.getAllPolicy();
        LoyaltyEventResult result = LoyaltyEventResult.builder().result(TransactionEventConstants.TransactionStatusType.FINISH).build();
        if (CollectionUtils.empty(policyList)) {
            // 根据分类和门店未匹配到有效的忠诚度政策。
            result.setExtMsg(I18N.text("sfa.loyalty.calc.not.find.policy"));
            result.setExtMsgKey("sfa.loyalty.calc.not.find.policy");
            return result;
        }
        sfaTaskRateLimiterService.getLoyaltyTaskLimiter().acquire();
        stopWatch.lap("rateLimiter");
        policyList.sort(Comparator.comparing(IncentivePolicy::getLastModifiedTime));

        getRuleFunction(param);
        // 匹配规则条件
        try {
            matchCondition(param, stopWatch);
        } catch (Exception e) {
            log.error("loyalty policy match fail。", e);
            result.setResult(TransactionEventConstants.TransactionStatusType.ERROR);
            // 忠诚度政策匹配出错。
            result.setExtMsgKey("sfa.loyalty.calc.policy.match.fail");
            result.setExtMsg(I18N.text("sfa.loyalty.calc.policy.match.fail"));
        }

        List<IncentivePolicy> polices = param.getCanUsePolicy();
        if (CollectionUtils.empty(polices)) {
            // 没有匹配到可以执行的政策。
            result.setExtMsg(I18N.text("sfa.loyalty.calc.match.not.policy"));
            result.setExtMsgKey("sfa.loyalty.calc.match.not.policy");
            return result;
        }
        List<String> policyIds = polices.stream()
                .map(IncentivePolicy::getId)
                .collect(Collectors.toList());
        List<IObjectData> allPolicyLog = policyLogDao.getAllPolicyLog(param.getEvent(), policyIds);
        Map<String, List<IObjectData>> logMap = Maps.newHashMap();
        for (IObjectData policyLog : allPolicyLog) {
            if (IncentivePolicyLogConstants.LogType.POLICY.getLogType().equals(policyLog.get(IncentivePolicyLogConstants.LOG_TYPE))) {
                logMap.computeIfAbsent(policyLog.get(IncentivePolicyLogConstants.POLICY_ID, String.class), k -> Lists.newArrayList()).add(policyLog);
            } else if (IncentivePolicyLogConstants.LogType.RULE.getLogType().equals(policyLog.get(IncentivePolicyLogConstants.LOG_TYPE))) {
                logMap.computeIfAbsent(policyLog.get(IncentivePolicyLogConstants.POLICY_RULE_ID, String.class), k -> Lists.newArrayList()).add(policyLog);
            } else if (IncentivePolicyLogConstants.LogType.ACTION.getLogType().equals(policyLog.get(IncentivePolicyLogConstants.LOG_TYPE))) {
                logMap.computeIfAbsent(policyLog.get(IncentivePolicyLogConstants.POLICY_RULE_ID, String.class)
                        + policyLog.get(IncentivePolicyLogConstants.POLICY_RULE_ACTION, String.class), k -> Lists.newArrayList()).add(policyLog);
            }
        }

        stopWatch.lap("getAllPolicyLog");
        param.setLogs(Lists.newArrayList());
        for (IncentivePolicy policy : polices) {
            // 判断政策是否已经执行过
            List<IObjectData> policyLog = logMap.get(policy.getId());
            if (hasExecLog(policyLog)) {
                log.warn("loyalty policy has calc, policy id {} , log id {} ", policy.getId(), policyLog.get(0).getId());
                continue;
            }
            param.setPolicy(policy);
            param.setDetailResult(Lists.newArrayList());
            log.info("事件ID：{}，开始执行政策ID：{}", param.getEvent().getId(), policy.getId());
            IncentivePolicyLogConstants.ExecuteResultType policyResult = execPolicy(param, stopWatch, logMap);
            log.info("事件ID：{}，执行政策ID：{}，执行结果：{}", param.getEvent().getId(), policy.getId(), policyResult.getExecuteResultType());
            // 记录政策执行日志
            policyLogDao.addPolicyLog(param, policyResult.getExecuteResultType(), null);
            if (!IncentivePolicyLogConstants.ExecuteResultType.SUCCESS.getExecuteResultType().equals(policyResult.getExecuteResultType())) {
                result.setResult(TransactionEventConstants.TransactionStatusType.ERROR);
            }
            stopWatch.lap("addPolicyLog" + policy.getId());
            saveEventResult(param, policy, policyResult);
            stopWatch.lap("saveEventResult");
        }
        if (CollectionUtils.notEmpty(param.getLogs())) {
            policyLogDao.batchAddLogs(param.getLogs(), param);
            stopWatch.lap("addPolicyAllLog");
        }
        result.setResult(getTransactionStatusType(param));

        return result;
    }

    private void saveEventResult(LoyaltyExecParam param, IncentivePolicy policy, IncentivePolicyLogConstants.ExecuteResultType policyResult) {
        if (!Boolean.TRUE.equals(param.createResult()) || CollectionUtils.empty(param.getDetailResult())) {
            return;
        }
        try {
            IObjectData eventResult = new ObjectData();
            eventResult.setName(policyLogDao.generateName());
            eventResult.set(Tenantable.TENANT_ID, param.getUser().getTenantId());
            eventResult.set(SystemConstants.Field.RecordType.apiName, SystemConstants.DEFAULT_RECORD_TYPE);
            eventResult.set(SystemConstants.Field.Owner.apiName, Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
            eventResult.setDescribeApiName(TransactionResultConstants.TRANSACTION_RESULT);
            eventResult.set(TransactionResultConstants.POLICY_ID, policy.getId());
            eventResult.set(TransactionResultConstants.EVENT_ID, param.getEvent().getId());
            eventResult.set(TransactionResultConstants.MEMBER_ID, param.getMemberId());
            eventResult.set(TransactionResultConstants.PROGRAM_ID, param.getEvent().get(TransactionEventConstants.PROGRAM_ID, String.class));
            if (Boolean.TRUE.equals(param.autoSubmit())) {
                eventResult.set(TransactionResultConstants.SEND_STATUS, TransactionResultConstants.SendStatusType.YES.getSendStatusType());
                eventResult.set(TransactionResultDetailConstants.EXECUTE_TIME, System.currentTimeMillis());
                if (!IncentivePolicyLogConstants.ExecuteResultType.SUCCESS.getExecuteResultType().equals(policyResult.getExecuteResultType())) {
                    eventResult.set(TransactionResultConstants.SEND_STATUS, TransactionResultConstants.SendStatusType.FAIL.getSendStatusType());
                }
            } else {
                eventResult.set(TransactionResultConstants.SEND_STATUS, TransactionResultConstants.SendStatusType.NO.getSendStatusType());
            }
            Map<String, List<IObjectData>> detailObjectDataMap = Maps.newHashMap();
            detailObjectDataMap.put(TransactionResultConstants.TRANSACTION_RESULT_DETAIL, param.getDetailResult());
            Map<String, IObjectDescribe> objectDescribesMap = serviceFacade.findObjects(param.getUser().getTenantId(),
                    Lists.newArrayList(TransactionResultConstants.TRANSACTION_RESULT,
                            TransactionResultConstants.TRANSACTION_RESULT_DETAIL));
            SaveMasterAndDetailData.Arg saveMasterDetailArg = SaveMasterAndDetailData.Arg.builder()
                    .masterObjectData(eventResult)
                    .detailObjectData(detailObjectDataMap)
                    .objectDescribes(objectDescribesMap)
                    .enableRealTimeCalculateDataAuth(Boolean.FALSE)
                    .build();
            SaveMasterAndDetailData.Result saveMasterDetailResult = serviceFacade.saveMasterAndDetailData(param.getUser(), saveMasterDetailArg);
            List<IObjectData> masterObjectData = Lists.newArrayList(saveMasterDetailResult.getMasterObjectData());
            Map<String, Object> callbackData = Maps.newHashMap();
            callbackData.put(ExtraDataKeys.TRIGGER_ACTION_CODE, ApprovalFlowTriggerType.CREATE.getActionCode());
            callbackData.put(ExtraDataKeys.DETAIL_CREATE_TIME, String.valueOf(saveMasterDetailResult.getMasterObjectData().getCreateTime()));
            Map<String, Map<String, Object>> callbackDataMap = Maps.newHashMap();
            callbackDataMap.put(saveMasterDetailResult.getMasterObjectData().getId(), callbackData);

            //1.触发审批流
            Map<String, ApprovalFlowStartResult> approvalFlowStartResultMap = serviceFacade.batchStartApproval(ApprovalFlowTriggerType.CREATE, param.getUser(), masterObjectData, Maps.newHashMap(), callbackDataMap, null);
            if (ApprovalFlowStartResult.SUCCESS.equals(approvalFlowStartResultMap.get(saveMasterDetailResult.getMasterObjectData().getId()))) {
                masterObjectData.get(0).set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.UnderReview.value);
                serviceFacade.batchUpdateByFields(param.getUser(), masterObjectData, Lists.newArrayList(SystemConstants.Field.LifeStatus.apiName));
                serviceFacade.updateDetailObjectDataLifeStatusWithDetailData(param.getUser(), masterObjectData.get(0), saveMasterDetailArg.getDetailObjectData().get(TransactionResultConstants.TRANSACTION_RESULT_DETAIL));
            }

            triggerWorkFlow(saveMasterDetailResult, objectDescribesMap, param.getUser());
        } catch (Exception e) {
            log.error("create transaction result error msg is {}", e.getMessage());
        }
    }

    private void triggerWorkFlow(SaveMasterAndDetailData.Result saveMasterDetailResult, Map<String, IObjectDescribe> objectDescribesMap, User user) {
        String masterId = saveMasterDetailResult.getMasterObjectData().getId();
        infraServiceFacade.startWorkFlow(masterId, saveMasterDetailResult.getMasterObjectData().getDescribeApiName(),
                WorkflowProducer.TRIGGER_START, user, Maps.newHashMap(), null);
        Map<String, List<IObjectData>> detailObjectData = Optional.ofNullable(saveMasterDetailResult.getDetailObjectData()).orElse(Maps.newHashMap());
        detailObjectData.forEach((key, value) -> {
            if (!TransactionResultConstants.TRANSACTION_RESULT_DETAIL.equals(key)) {
                return;
            }
            if (CollectionUtils.empty(value)) {
                return;
            }
            for (IObjectData objectData : value) {
                infraServiceFacade.startWorkFlow(objectData.getId(), key, WorkflowProducer.TRIGGER_START, user, Maps.newHashMap(), null);

            }
        });
    }

    private IncentivePolicyLogConstants.ExecuteResultType execPolicy(LoyaltyExecParam param, StopWatch stopWatch, Map<String, List<IObjectData>> logMap) {

        // 执行规则
        IncentivePolicy policy = param.getPolicy();
        List<IncentivePolicyRule> rules = policy.getRules();
        boolean hasSuccessAction = false;
        boolean hasFailAction = false;
        for (IncentivePolicyRule rule : rules) {
            // 判断政策是否已经执行过
            List<IObjectData> ruleLog = logMap.get(rule.getId());
            if (hasExecLog(ruleLog)) {
                log.warn("loyalty rule has calc, rule id {} , log id {} ", rule.getId(), ruleLog.get(0).getId());
                continue;
            }
            List<IncentivePolicyAction> actions = rule.getActions();
            param.setUseRule(rule);
            log.info("事件ID：{}，政策ID：{}，，开始规则ID：{}", param.getEvent().getId(), policy.getId(), rule.getId());
            // 执行动作
            IncentivePolicyLogConstants.ExecuteResultType resultType = execActions(actions, param, stopWatch, logMap);
            log.info("事件ID：{}，政策ID：{}，规则ID：{}，执行结果：{}", param.getEvent().getId(), policy.getId(), rule.getId(), resultType.getExecuteResultType());
            if (IncentivePolicyLogConstants.ExecuteResultType.SUCCESS.getExecuteResultType().equals(resultType.getExecuteResultType())) {
                hasSuccessAction = true;
            }
            if (IncentivePolicyLogConstants.ExecuteResultType.PART.getExecuteResultType().equals(resultType.getExecuteResultType())) {
                hasFailAction = true;
                hasSuccessAction = true;
            }
            if (IncentivePolicyLogConstants.ExecuteResultType.FAIL.getExecuteResultType().equals(resultType.getExecuteResultType())) {
                hasFailAction = true;
            }

            // 记录政策执行日志
            policyLogDao.addRuleLog(param, resultType.getExecuteResultType(), null);
            stopWatch.lap("addRuleLog" + rule.getId());
        }

        return getExecuteResultType(hasSuccessAction, hasFailAction);
    }

    private IncentivePolicyLogConstants.ExecuteResultType execActions(List<IncentivePolicyAction> actions, LoyaltyExecParam param, StopWatch stopWatch, Map<String, List<IObjectData>> logMap) {
        boolean hasSuccessAction = false;
        boolean hasFailAction = false;
        for (IncentivePolicyAction action : actions) {
            // 判断动作是否已经执行过
            List<IObjectData> actionLog = logMap.get(param.getUseRule().getId() + action.getName());
            if (hasExecLog(actionLog)) {
                log.warn("loyalty action has calc, rule id {} , log id {} ", param.getUseRule().getId() + action.getName(), actionLog.get(0).getId());
                continue;
            }
            String actionType = action.getActionType();
            try {
                log.info("事件ID：{}，政策ID：{}，规则ID：{}，开始动作名称：{}", param.getEvent().getId(), param.getPolicy().getId(), param.getUseRule().getId(), action.getName());
                param.setActionName(action.getName());
                LoyaltyActionResult actionResult = actionServiceManager.getActionService(actionType).execAction(param, action, stopWatch);
                log.info("事件ID：{}，政策ID：{}，规则ID：{}，动作名称：{}，执行结果：{}", param.getEvent().getId(), param.getPolicy().getId(), param.getUseRule().getId(), action.getName(), actionResult.isSuccess() ? "成功" : "失败");
                stopWatch.lap(action.getName());
                if (actionResult.isSuccess()) {
                    hasSuccessAction = true;
                } else {
                    hasFailAction = true;
                    param.setHasFailAction(true);
                    param.setActionFailMsg(actionResult.getMsg());
                }
            } catch (Exception e) {
                log.error("exec action fail" + action.getName(), e);
                hasFailAction = true;
                param.setHasFailAction(true);
            }

        }
        stopWatch.lap("execActions");
        return getExecuteResultType(hasSuccessAction, hasFailAction);
    }

    @NotNull
    private IncentivePolicyLogConstants.ExecuteResultType getExecuteResultType(boolean hasSuccessAction, boolean hasFailAction) {
        IncentivePolicyLogConstants.ExecuteResultType resultType = IncentivePolicyLogConstants.ExecuteResultType.SUCCESS;
        if (hasFailAction) {
            if (hasSuccessAction) {
                resultType = IncentivePolicyLogConstants.ExecuteResultType.PART;
            } else {
                resultType = IncentivePolicyLogConstants.ExecuteResultType.FAIL;
            }
        }
        return resultType;
    }

    private void matchCondition(LoyaltyExecParam param, StopWatch stopWatch) {
        List<IncentivePolicy> allPolicy = param.getAllPolicy();
        //过滤掉规则条件都为空的政策无需调规则引擎
        List<String> validPolicyIds = allPolicy.stream()
                .filter(IncentivePolicy::getHasRuleCondition)
                .map(IncentivePolicy::getId).collect(Collectors.toList());
        // 补充高级属性值
        fillAttr(param, allPolicy);
        // 补充条件中的变量值
        fillRuleVariable(param);
        stopWatch.lap("fillAttr");
        // 使用规则引擎匹配规则
        Map<String, List<String>> matchedPolicyIdToRuleIds = engineLogicService.matchRuleCondition(param.getUser(), validPolicyIds
                , param.getEvent().getDescribeApiName(), param.getEvent(), param.getDetail(), null);
        stopWatch.lap("engineLogicService.matchRuleCondition");
        log.info("事件ID：{},规则引擎匹配结果:{}", param.getEvent().getId(), matchedPolicyIdToRuleIds);
        for (IncentivePolicy pricePolicy : allPolicy) {
            //政策内包含的规则为：已命中规则+条件为空的规则
            List<IncentivePolicyRule> emptyRule = pricePolicy.getRules().stream()
                    .filter(x -> !x.isHasRuleCondition())
                    .collect(Collectors.toList());
            List<IncentivePolicyRule> matchRules = Lists.newArrayList();
            List<IncentivePolicyRule> allRules = pricePolicy.getRules();
            if (matchedPolicyIdToRuleIds.containsKey(pricePolicy.getId())) {
                List<String> ruleIds = matchedPolicyIdToRuleIds.get(pricePolicy.getId());
                matchRules = allRules.stream().filter(x -> ruleIds.contains(x.getId())).collect(Collectors.toList());
            }
            matchRules.addAll(emptyRule);
            // 匹配政策相关的高级属性
            matchPolicyCondition(param, pricePolicy, matchRules);
            log.info("事件ID：{},匹配政策相关的高级属性结果，pricePolicyId:{},matchRuleIds:{}--matchPolicyCondition", param.getEvent().getId(), pricePolicy.getId(), matchRules);

            // 组号，最高优先级规则
            Map<Integer, IncentivePolicyRule> groupMinRule = new HashMap<>();
            for (IncentivePolicyRule rule : matchRules) {
                IncentivePolicyRule oldRule = groupMinRule.get(rule.getGroupNo());
                if (null == oldRule || rule.getOrderField() < oldRule.getOrderField()) {
                    groupMinRule.put(rule.getGroupNo(), rule);
                }
            }
            // 使用函数匹配规则
            matchConditionFunc(param, allRules, groupMinRule, pricePolicy);
            log.info("事件ID：{},函数匹配结果：pricePolicy:{},groupMinRule:{}", param.getEvent().getId(), pricePolicy.getId(), groupMinRule);

            if (CollectionUtils.notEmpty(groupMinRule)) {
                pricePolicy.setRules(Lists.newArrayList(groupMinRule.values()));
                param.getCanUsePolicy().add(pricePolicy);
            }
        }
        log.info("事件ID：{},canUsePolicyId:{},canUseRuleId:{}", param.getEvent().getId(),
                param.getCanUsePolicy().stream().map(IncentivePolicy::getId).collect(Collectors.toList()),
                param.getCanUsePolicy().stream().map(IncentivePolicy::getRules).flatMap(List::stream).map(IncentivePolicyRule::getId).collect(Collectors.toList()));
        stopWatch.lap("matchRuleCondition");
    }

    private void matchPolicyCondition(LoyaltyExecParam param, IncentivePolicy pricePolicy, List<IncentivePolicyRule> matchRules) {
        for (int i = matchRules.size() - 1; i >= 0; i--) {
            IncentivePolicyRule rule = matchRules.get(i);
            if (!rule.isHasPolicyCondition()) {
                return;
            }
            if (hasNotMatchCondition(param, pricePolicy, rule)) {
                matchRules.remove(i);
            }

        }
    }

    private boolean hasNotMatchCondition(LoyaltyExecParam param, IncentivePolicy pricePolicy, IncentivePolicyRule rule) {
        List<RuleWhere.FiltersBean> conditionRules = rule.getAttributeDataList();
        for (RuleWhere.FiltersBean conditionRule : conditionRules) {
            String objApiName = conditionRule.getBindObjectApiName();
            if (IncentiveMetricConstants.EXTENDED_ATTRIBUTE_POLICY.equals(objApiName) || IncentiveMetricConstants.EXTENDED_ATTRIBUTE_MEMBER_INCENTIVE.equals(objApiName)) {
                Object value = param.getPolicyExtAttrValue().get(objApiName + conditionRule.getExtApiName());
                Object valueDb = param.getPolicyExtAttrValue().get(objApiName + conditionRule.getExtApiName() + pricePolicy.getId());
                if (null != valueDb) {
                    value = valueDb;
                }
                if (!ExtOperatorUtil.extOperatorMatch(conditionRule, value)) {
                    return true;
                }
            }
        }

        return false;
    }

    private void fillAttr(LoyaltyExecParam param, List<IncentivePolicy> allPolicy) {
        List<RuleWhere.FiltersBean> arrRule = allPolicy.stream()
                .flatMap(p -> p.getRules().stream())
                .flatMap(r -> r.getAttributeDataList().stream())
                .collect(Collectors.toList());
        if (CollectionUtils.empty(arrRule)) {
            return;
        }

        Map<String, List<String>> objRules = Maps.newHashMap();
        Map<String, RuleWhere.FiltersBean> condRules = Maps.newHashMap();
        for (RuleWhere.FiltersBean filtersBean : arrRule) {
            String objApiName = filtersBean.getBindObjectApiName();
            List<String> list = objRules.computeIfAbsent(objApiName, k -> new ArrayList<>());
            list.add(filtersBean.getExtApiName());
            condRules.put(objApiName + filtersBean.getExtApiName(), filtersBean);
            setExtDefaultValue(param, filtersBean, objApiName);
        }

        for (Map.Entry<String, List<String>> entry : objRules.entrySet()) {
            String objApiName = entry.getKey();
            List<IObjectData> dataList = extendedAttributeDao.getExtendedAttributeList(param, entry.getKey(), entry.getValue(), null);
            for (IObjectData att : dataList) {
                RuleWhere.FiltersBean condRule = condRules.get(entry.getKey() + att.get(ExtendedAttributeConstants.ATTR_API_NAME, String.class));
                String returnType = condRule.getReturnType();
                String valueKey = ExtendedAttributeConstants.DATA_TYPE_TO_API_NAME_MAP.get(returnType);
                Object value = extendedAttributeDao.getDbRealValue(returnType, valueKey, att);
                if (IncentiveMetricConstants.EXTENDED_ATTRIBUTE_MEMBER.equals(objApiName) || IncentiveMetricConstants.EXTENDED_ATTRIBUTE_EVENT.equals(objApiName)) {
                    param.getEvent().set(condRule.getExtApiName(), value);
                } else {
                    param.getPolicyExtAttrValue().put(objApiName + condRule.getExtApiName() + att.get(ExtendedAttributeConstants.POLICY_ID, String.class), value);
                }
            }
        }
    }

    private void setExtDefaultValue(LoyaltyExecParam param, RuleWhere.FiltersBean filtersBean, String objApiName) {
        if (StringUtils.isNotBlank(filtersBean.getExtDefaultValue())) {
            if (IncentiveMetricConstants.EXTENDED_ATTRIBUTE_MEMBER.equals(objApiName) || IncentiveMetricConstants.EXTENDED_ATTRIBUTE_EVENT.equals(objApiName)) {
                param.getEvent().set(filtersBean.getExtApiName(), extendedAttributeDao.getRealValue(filtersBean.getReturnType(), filtersBean.getExtDefaultValue()));
            } else {
                param.getPolicyExtAttrValue().put(objApiName + filtersBean.getExtApiName(), extendedAttributeDao.getRealValue(filtersBean.getReturnType(), filtersBean.getExtDefaultValue()));
            }
        }
    }

    private void matchConditionFunc(LoyaltyExecParam param, List<IncentivePolicyRule> allRules
            , Map<Integer, IncentivePolicyRule> groupMinRule
            , IncentivePolicy pricePolicy) {
        Map<Integer, List<IncentivePolicyRule>> aplRules = new HashMap<>();
        for (IncentivePolicyRule rule : allRules) {
            if (CollectionUtils.notEmpty(rule.getRuleFunctionDataList())) {
                List<IncentivePolicyRule> old = aplRules.computeIfAbsent(rule.getGroupNo(), k -> new ArrayList<>());
                old.add(rule);
            }
        }

        for (Map.Entry<Integer, List<IncentivePolicyRule>> aplRule : aplRules.entrySet()) {
            List<IncentivePolicyRule> rules = aplRule.getValue();
            rules.sort(Comparator.comparingInt(IncentivePolicyRule::getOrderField));
            for (IncentivePolicyRule rule : rules) {
                IncentivePolicyRule oldRule = groupMinRule.get(rule.getGroupNo());
                if (null != oldRule && oldRule.getOrderField() < rule.getOrderField()) {
                    continue;
                }
                Map<String, List<IObjectData>> rulesParam = Maps.newHashMap();
                RuleWhere.FiltersBean where = rule.getRuleFunctionDataList().get(0);
                rulesParam.put(IncentivePolicyConstants.INCENTIVE_POLICY_RULE, Lists.newArrayList(rule.getSrcData()));
                Object ret = functionService.executeEventFunc(pricePolicy.getSrcData(), rulesParam, param.getEvent(), null, where.getAPLApiName());
                log.info("事件ID：{}，函数执行结果：{}", param.getEvent().getId(), ret);
                if (ExtOperatorUtil.extOperatorMatch(where, ret)) {
                    groupMinRule.put(rule.getGroupNo(), rule);
                }
            }
        }
    }


    private void getRuleFunction(LoyaltyExecParam param) {
        List<IncentivePolicy> allPolicy = param.getAllPolicy();
        for (IncentivePolicy policy : allPolicy) {
            List<IncentivePolicyRule> ruleList = policy.getRules();
            for (IncentivePolicyRule rule : ruleList) {
                if (CollectionUtils.empty(rule.getRuleFunctionDataList())) {
                    continue;
                }
                param.getConditionRules().put(rule.getId(), rule.getRuleFunctionDataList());
            }
        }
    }

    public void back(IObjectData event, String userId) {
        long start = System.currentTimeMillis();
        AuditArg auditArg = this.getEventLogInfo(event);
        Date execDate = new Date();
        String tenantId = event.getTenantId();
        String status = event.get(TransactionEventConstants.TRANSACTION_STATUS, String.class);
        if (TransactionEventConstants.TransactionStatusType.QUEUE.getTransactionStatusType().equals(status)) {
            log.warn("loyalty event transaction status is queue, event id {}", event.getId());
            sendAuditLog(auditArg, "loyalty event transaction status is queue");
            return;
        }

        if (StringUtils.isBlank(userId)) {
            userId = User.SUPPER_ADMIN_USER_ID;
        }

        User user = new User(tenantId, userId);
        // 判断租户是否已经执行过
        List<IObjectData> eventLogs = policyLogDao.getEventLog(event);
        if (!hasExecLog(eventLogs)) {
            log.warn("loyalty event has not calc, event id {}", event.getId());
            // 事件没有执行，回退完成。
            sendMsg(event, user, LoyaltyCalcConstants.ManualType.BACK, false, I18N.text("sfa.loyalty.calc.event.not.calc.fallback.success"));
            sendAuditLog(auditArg, "loyalty event has not calc");
            return;
        }

        List<IObjectData> acLogs = policyLogDao.getLogByType(event, IncentivePolicyLogConstants.LogType.ACTION.getLogType(), IncentivePolicyLogConstants.ExecuteResultType.SUCCESS.getExecuteResultType());
        Map<String, Object> extData = Maps.newHashMap();
        if (CollectionUtils.empty(acLogs)) {
            // 没有执行成功的动作，回退完成。
            String msg = I18N.text("sfa.loyalty.calc.event.not.action.fallback.success");
            extData.put(IncentivePolicyLogConstants.REASON, msg);
            policyLogDao.addEventLog(event, extData, IncentivePolicyLogConstants.ExecuteResultType.SUCCESS.getExecuteResultType());
            event.set(TransactionEventConstants.TRANSACTION_STATUS, TransactionEventConstants.TransactionStatusType.ROLLBACK_FINISH.getTransactionStatusType());
            sendMsg(event, user, LoyaltyCalcConstants.ManualType.BACK, false, msg);
            sendAuditLog(auditArg, msg);
            return;
        }

        LoyaltyExecParam param = LoyaltyExecParam.builder()
                .event(event)
                .memberId(event.get(TransactionEventConstants.MEMBER_ID, String.class))
                .user(user)
                .execDate(execDate).build();
        addLogDescribe(param);
        boolean hasFail = false;
        StringJoiner failActions = new StringJoiner(",");
        for (IObjectData log : acLogs) {
            String actionType = log.get(IncentivePolicyLogConstants.ACTION_TYPE, String.class);
            LoyaltyActionResult result = actionServiceManager.getActionService(actionType).back(param, log);
            if (!result.isSuccess()) {
                hasFail = true;
                failActions.add(log.get(IncentivePolicyLogConstants.POLICY_RULE_ACTION, String.class));
            }
        }

        if (Boolean.TRUE.equals(event.get(TransactionEventConstants.CREATE_RESULT_RECORD, Boolean.class))) {
            List<IObjectData> result = detailDao.getResult(event);
            if (CollectionUtils.notEmpty(result)) {
                List<IObjectData> details = detailDao.getDetails(result);
                serviceFacade.bulkDeleteDirect(details, user);
                serviceFacade.bulkDeleteDirect(result, user);
            }
        }

        String msg;
        if (hasFail) {
            msg = failActions + I18N.text("sfa.loyalty.calc.fallback.fail");
            extData.put(IncentivePolicyLogConstants.REASON, msg);
            event.set(TransactionEventConstants.TRANSACTION_STATUS, TransactionEventConstants.TransactionStatusType.ROLLBACK_ERROR.getTransactionStatusType());
        } else {
            msg = I18N.text("sfa.loyalty.calc.fallback.success");
            extData.put(IncentivePolicyLogConstants.REASON, msg);
            event.set(TransactionEventConstants.TRANSACTION_STATUS, TransactionEventConstants.TransactionStatusType.ROLLBACK_FINISH.getTransactionStatusType());
        }

        policyLogDao.batchUpdateByFieldSkip(user, Lists.newArrayList(event), Lists.newArrayList(TransactionEventConstants.TRANSACTION_STATUS));
        policyLogDao.addEventLog(event, extData, hasFail ? IncentivePolicyLogConstants.ExecuteResultType.FAIL.getExecuteResultType() : IncentivePolicyLogConstants.ExecuteResultType.SUCCESS.getExecuteResultType());
        sendMsg(event, user, LoyaltyCalcConstants.ManualType.BACK, hasFail, "");
        auditArg.setCost(System.currentTimeMillis() - start);
        sendAuditLog(auditArg, msg);
    }


    private void sendMsg(IObjectData event, User user, LoyaltyCalcConstants.ManualType type, boolean fail, String extMsg) {
        String contentKey;
        List<String> fullContentInternationalParameters = Lists.newArrayList(event.getName());
        if (LoyaltyCalcConstants.ManualType.BACK.equals(type)) {
            //事件{0}回退成功。
            contentKey = "sfa.loyalty.calc.back.success";
            if (fail) {
                //事件{0}回退失败。
                contentKey = "sfa.loyalty.calc.back.fail";
            }
        } else if (LoyaltyCalcConstants.ManualType.RETRY.equals(type)) {
            contentKey = "sfa.loyalty.calc.retry.success";
            //事件{0}重试成功。
            if (fail) {
                contentKey = "sfa.loyalty.calc.retry.fail";
                //"事件{0}重试失败。
            }
        } else {
            return;
        }
        String content = I18N.text(contentKey, event.getName());
        String title = I18N.text("sfa.loyalty.calc.title");
        String titleKey = "sfa.loyalty.calc.title";
        sendCrmNotice(user.getTenantId(), user.getUserId(), title, content, titleKey, contentKey, fullContentInternationalParameters);
    }


    private void sendCrmNotice(String tenantId, String receiverId, String title, String content, String i18nKeyTitle, String i18nKeyContent
            , List<String> fullContentInternationalParameters) {
        Set<Integer> receiverIds = Sets.newHashSet();
        if (NumberUtils.isNumber(receiverId)) {
            receiverIds.add(Integer.parseInt(receiverId));
        } else {
            log.error("userid is not number,userid:{}", receiverId);
            return;
        }
        CRMNotification crmNotification = CRMNotification.builder()
                .sender(User.SUPPER_ADMIN_USER_ID)
                .remindRecordType(92)
                .content(content)
                .title(title)
                .dataId("")
                .content2Id("0")
                .receiverIds(receiverIds)
                .build();
        User superAdminUser = User.systemUser(tenantId);
        crmNotificationService.sendCRMNotification(superAdminUser, crmNotification);
        //发送新crm通知
        CRMRecordUtil.sendNewCRMRecord(crmNotificationService, superAdminUser, 92, Lists.newArrayList(receiverIds),
                User.SUPPER_ADMIN_USER_ID, title, content, i18nKeyTitle,
                Lists.newArrayList(), i18nKeyContent, fullContentInternationalParameters,
                null);
    }

    public void fillRuleVariable(LoyaltyExecParam param) {
        List<String> metricIdList = new ArrayList<>();
        Map<String, List<RuleWhere.FiltersBean>> filtersMap = param.getConditionRules();
        for (Map.Entry<String, List<RuleWhere.FiltersBean>> entry : filtersMap.entrySet()) {
            List<RuleWhere.FiltersBean> filterList = entry.getValue();
            for (RuleWhere.FiltersBean filter : filterList) {
                if ("variable".equals(filter.getRightValueType())) {
                    if ("metric".equals(filter.getRightFieldNameType())) {
                        metricIdList.addAll(filter.getFieldValues());
                    }
                }
            }
        }
        Map<String, Object> valueMap = incentiveMetricService.getValue(param, metricIdList);
        for (Map.Entry<String, Object> entry : valueMap.entrySet()) {
            param.getEvent().set(entry.getKey(), entry.getValue());
        }
    }
}
