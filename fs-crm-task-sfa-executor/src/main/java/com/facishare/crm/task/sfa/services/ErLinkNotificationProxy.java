package com.facishare.crm.task.sfa.services;

import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.User;
import com.fxiaoke.enterpriserelation2.arg.UnionMessageSendArg;
import com.fxiaoke.enterpriserelation2.arg.UnionTextMessage;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.service.UnionMessageService;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 * ============================================================
 *
 * @Description:
 * @CreatedBy: Sundy on 2025-05-16
 * ============================================================
 */
@Service
@Slf4j
public class ErLinkNotificationProxy {
    @Resource
    private UnionMessageService unionMessageService;


    public void pushNotification(User user, Set<Long> receiverIds, String content) {
        UnionTextMessage unionTextMessage = UnionTextMessage.buildTextMessage(content);
        UnionMessageSendArg arg = new UnionMessageSendArg<UnionTextMessage>();
        arg.setChannels(Sets.newHashSet(16));
        arg.setFsAppId(AppIdMapping.getAppIdByName(AppIdMapping.PRM_APP_ID));
        arg.setSenderEi(user.getTenantIdInt());
        arg.setReceiverIds(receiverIds);
        arg.setLinkType(1);
        arg.setMessageType(0);
        arg.setMessage(unionTextMessage);
        try {
            log.info("Sending message to ErLinkNotificationProxy, arg:{}", arg);
            unionMessageService.sendMessage(HeaderObj.newInstance(user.getTenantIdInt()), arg);
        } catch (Exception e) {
            log.warn("ErLinkNotificationProxy#pushNotification failed, user:{}, receiverIds:{}, content:{}", user, receiverIds, content, e);
        }
    }
}
