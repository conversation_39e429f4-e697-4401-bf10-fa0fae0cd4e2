package com.facishare.crm.task.sfa.services.loyalty;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.task.sfa.model.loyalty.MetricModel;
import com.facishare.crm.task.sfa.services.loyalty.dao.ExtendedAttributeDao;
import com.facishare.crm.task.sfa.services.loyalty.dto.LoyaltyExecParam;
import com.facishare.crm.task.sfa.services.loyalty.util.ExtendedAttributeConstants;
import com.facishare.crm.task.sfa.services.loyalty.util.IncentiveMetricConstants;
import com.facishare.crm.task.sfa.services.rebate.ExecuteFunctionServiceImpl;
import com.facishare.crm.task.sfa.services.rebate.rest.RuleEngineLogicService;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
public class IncentiveMetricService {

    @Resource
    ServiceFacade serviceFacade;
    @Resource
    RuleEngineLogicService engineLogicService;
    @Resource
    ExtendedAttributeDao extendedAttributeDao;
    @Resource
    ExecuteFunctionServiceImpl functionService;

    public Object getValue(LoyaltyExecParam param, String metricId) {
        Map<String, Object> dataMap = getValue(param, Lists.newArrayList(metricId));
        return dataMap.get(metricId);
    }

    public Map<String, Object> getValue(LoyaltyExecParam param, List<String> metricIds) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), IObjectData.ID, metricIds);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(param.getUser(), IncentiveMetricConstants.INCENTIVE_METRIC, searchTemplateQuery);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return new HashMap<>();
        }
        List<IObjectData> metricDataList = queryResult.getData();
        //参数预处理
        Set<String> aggregateIdSets = new HashSet<>();
        List<String> extendedAttributeIdList = new ArrayList<>();
        for (IObjectData metricData : metricDataList) {
            String metricType = metricData.get(IncentiveMetricConstants.METRIC_TYPE, String.class);
            String metricInfoJsonStr = metricData.get(IncentiveMetricConstants.METRIC_INFO, String.class);
            if ("aggregate".equals(metricType)) {
                MetricModel.AggregateInfo aggregateInfo = JSON.parseObject(metricInfoJsonStr, MetricModel.AggregateInfo.class);
                aggregateIdSets.add(aggregateInfo.getAggregateId());
            } else if ("attribute".equals(metricType)) {
                MetricModel.AttributeInfo attributeInfo = JSON.parseObject(metricInfoJsonStr, MetricModel.AttributeInfo.class);
                extendedAttributeIdList.add(attributeInfo.getAttributeId());
            }
        }
        Map<String, String> aggregateIdToValue = engineLogicService.computeAggregateValues(param.getUser(),
                aggregateIdSets, param.getEvent().getDescribeApiName(), param.getEvent(), param.getDetail(), null);
        Map<String, IObjectData> extendedAttributeMap = findExtendedAttributeMap(param.getUser().getTenantId(), extendedAttributeIdList);
        return fillResult(param, metricDataList, aggregateIdToValue, extendedAttributeMap);
    }

    public Map<String, IObjectData> findExtendedAttributeMap(String tenantId, List<String> ids) {
        Map<String, IObjectData> extendedAttributeMap = new HashMap<>();
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), IObjectData.ID, ids);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(User.systemUser(tenantId), IncentiveMetricConstants.EXTENDED_ATTRIBUTE, searchTemplateQuery);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getData())) {
            return extendedAttributeMap;
        }
        for (IObjectData extendedAttributeData : queryResult.getData()) {
            extendedAttributeMap.put(extendedAttributeData.getId(), extendedAttributeData);
        }
        return extendedAttributeMap;
    }

    public Map<String, Object> fillResult(LoyaltyExecParam param, List<IObjectData> metricDataList,
                                          Map<String, String> aggregateIdToValue,
                                          Map<String, IObjectData> extendedAttributeMap) {
        Map<String, Object> dataMap = new HashMap<>();
        for (IObjectData metricData : metricDataList) {
            String metricType = metricData.get(IncentiveMetricConstants.METRIC_TYPE, String.class);
            String metricInfoJsonStr = metricData.get(IncentiveMetricConstants.METRIC_INFO, String.class);
            if (IncentiveMetricConstants.MetricType.FIELD.getMetricType().equals(metricType)) {
                //字段类型,无需计算结果,按规则引擎保存规则即可,规则引擎会自动计算值
            } else if (IncentiveMetricConstants.MetricType.AGGREGATE.getMetricType().equals(metricType)) {
                MetricModel.AggregateInfo aggregateInfo = JSON.parseObject(metricInfoJsonStr, MetricModel.AggregateInfo.class);
                Object dataValue = aggregateIdToValue.get(aggregateInfo.getAggregateId());
                dataMap.put(metricData.getId(), dataValue);
            } else if (IncentiveMetricConstants.MetricType.APL.getMetricType().equals(metricType)) {
                MetricModel.APLInfo aplInfo = JSON.parseObject(metricInfoJsonStr, MetricModel.APLInfo.class);
                Object dataValue = functionService.executeEventFunc(param.getPolicy().getSrcData(), null, param.getEvent(), null, aplInfo.getAplApiName());
                dataMap.put(metricData.getId(), dataValue);
            } else if (IncentiveMetricConstants.MetricType.ATTRIBUTE.getMetricType().equals(metricType)) {
                MetricModel.AttributeInfo attributeInfo = JSON.parseObject(metricInfoJsonStr, MetricModel.AttributeInfo.class);
                IObjectData extendedAttributeData = extendedAttributeMap.get(attributeInfo.getAttributeId());
                String attrType = extendedAttributeData.get(ExtendedAttributeConstants.ATTR_TYPE, String.class);
                String objApiName = ExtendedAttributeConstants.ATTR_OBJECT_MAP.get(attrType);
                IObjectData extendedAttributeValue = extendedAttributeDao.getExtendedAttributeList(param, objApiName, attributeInfo.getAttributeFieldApiName());
                Object dataValue;
                if (extendedAttributeValue == null) {
                    dataValue = extendedAttributeData.get(ExtendedAttributeConstants.DEFAULT_VALUE);
                } else {
                    dataValue = extendedAttributeValue.get(ExtendedAttributeConstants.NUMBER_VALUE);
                }
                dataMap.put(metricData.getId(), dataValue);
            }
        }
        return dataMap;
    }
}
