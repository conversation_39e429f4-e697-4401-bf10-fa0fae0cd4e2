package com.facishare.crm.task.sfa.objchange.service.handler;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.objchange.model.ObjectChangeMessage;
import com.facishare.crm.task.sfa.services.PaymentPlanOverTimeService;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class PaymentPlanHandler implements ObjectChangeHandler {

    @Resource
    private NomonProducer nomonProducer;

    @Override
    public String getApiName() {
        return OBJECT_DESCRIBE_API_NAME;
    }

    @Autowired
    PaymentPlanOverTimeService paymentPlanOverTimeService;
    @Autowired
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;
    @Autowired
    protected MetaDataService metaDataService;

    private static final String ACTUAL_PAYMENT_AMOUNT_FIELD_NAME = "actual_payment_amount";

    private static final String PLAN_PAYMENT_TIME = "plan_payment_time";

    private static final String OBJECT_DESCRIBE_API_NAME = "PaymentPlanObj";

    private static final String PLAN_PAYMENT_AMOUNT_FIELD_NAME = "plan_payment_amount";


    @Override
    public void doService(ObjectChangeMessage msg, MessageExt messageExt) {
        try {
            sfaTaskRateLimiterService.getPaymentPlanTimeCalcLimiter().acquire();
            if (msg.getBody() != null && msg.getBody().size() > 0 && ("u".equals(msg.getOp()) || "i".equals(msg.getOp()))) {
                String tenantId = msg.getTenantId();
                List<ObjectChangeMessage.Content> changeContent = msg.getBody();
                for (ObjectChangeMessage.Content content : changeContent) {
                    String objectId = content.getObjectId();
                    List<String> updateFields = Lists.newArrayList(ACTUAL_PAYMENT_AMOUNT_FIELD_NAME, PLAN_PAYMENT_AMOUNT_FIELD_NAME, PLAN_PAYMENT_TIME);
                    boolean isInsert = "i".equals(msg.getOp());
                    boolean afterChanged = content.getAfterTriggerData() != null && TriggerDataContainsKey(content.getAfterTriggerData(), updateFields);
                    boolean beforeChanged = content.getBeforeTriggerData() != null && TriggerDataContainsKey(content.getBeforeTriggerData(), updateFields);
                    boolean needUpdatePaymentPlan = isInsert || afterChanged || beforeChanged;
                    if (needUpdatePaymentPlan) {
                        paymentPlanOverTimeService.updatePaymentPlanStatus(tenantId, objectId);
                    }
                    boolean planPaymentTimeChanged =
                            (content.getAfterTriggerData() != null && content.getAfterTriggerData().containsKey(PLAN_PAYMENT_TIME))
                                    || (content.getBeforeTriggerData() != null && content.getBeforeTriggerData().containsKey(PLAN_PAYMENT_TIME));
                    boolean needSendOverTimeTask = isInsert || planPaymentTimeChanged;
                    if (needSendOverTimeTask && !Strings.isNullOrEmpty(objectId)) {
                        sendOverTimeTask(tenantId, objectId);
                    }
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private boolean TriggerDataContainsKey(JSONObject object, List<String> keys) {
        boolean result = false;
        for (String key : keys) {
            result = object.containsKey(key);
            if (result) {
                return result;
            }
        }
        return result;
    }

    private void sendOverTimeTask(String tenantId, String dataId) {
        try {
            IObjectData data = metaDataService.findObjectDataIgnoreAll(User.systemUser(tenantId), dataId, OBJECT_DESCRIBE_API_NAME);
            if (data.get(PLAN_PAYMENT_TIME) == null) {
                log.warn(String.format("tenantId %s dataId %s plan_payment_time null", tenantId, dataId));
            } else {
                Long planPaymentTime = data.get(PLAN_PAYMENT_TIME, Long.class);
                Long overdueTimeMillis = planPaymentTime + 86400000;
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String time = format.format(overdueTimeMillis);
                Date overdueTime = format.parse(time);
                NomonMessage message = NomonMessage
                        .builder()
                        .biz("payment_plan_overdue")
                        .tenantId(tenantId)
                        .dataId(dataId)
                        .executeTime(overdueTime)
                        .callArg(String.format("{\"tenantId\":\"%s\",\"dataId\":\"%s\"}", tenantId, dataId))
                        .build();
                nomonProducer.send(message);
            }
        } catch (ObjectDataNotFoundException ex) {
            log.warn("send nomon msg error", ex);
        } catch (Exception ex) {
            log.error("send nomon msg error", ex);
        }
    }
}
