package com.facishare.crm.task.sfa.services.loyalty.dao;

import com.alibaba.fastjson.util.TypeUtils;
import com.facishare.crm.task.sfa.loyalty.service.LoyaltyMemberQueryService;
import com.facishare.crm.task.sfa.model.loyalty.IncentivePolicy;
import com.facishare.crm.task.sfa.model.loyalty.IncentivePolicyAction;
import com.facishare.crm.task.sfa.services.loyalty.dto.LoyaltyExecParam;
import com.facishare.crm.task.sfa.services.loyalty.util.ExtendedAttributeConstants;
import com.facishare.crm.task.sfa.services.loyalty.util.IncentiveMetricConstants;
import com.facishare.crm.task.sfa.services.loyalty.util.TransactionEventConstants;
import com.facishare.crm.task.sfa.services.rebate.dao.AbsQueryDao;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExtendedAttributeDao extends AbsQueryDao {
    @Resource
    protected LoyaltyMemberQueryService memberQueryService;

    public List<IObjectData> getExtendedAttributeList(LoyaltyExecParam param, IncentivePolicyAction action, String metricId) {
        IncentivePolicyAction.ValueMetricInfo metricInfo = action.getValueMetricInfo().get(metricId);
        return getExtendedAttributeList(param, metricInfo, getMemberId(param, action));
    }

    public List<IObjectData> getExtendedAttributeList(LoyaltyExecParam param, IncentivePolicyAction.ValueMetricInfo metricInfo, String memberId) {
        String objApiName = metricInfo.getBindObjectApiName();
        String fieldApiName = metricInfo.getExtApiName();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, ExtendedAttributeConstants.ATTR_API_NAME, fieldApiName);
        addExtFixFilter(param, objApiName, filters, Lists.newArrayList(param.getPolicy().getId()), memberId);
        return getBaseData(filters, param.getUser().getTenantId(), objApiName);
    }

    public IObjectData getExtendedAttributeList(LoyaltyExecParam param, String objApiName, String fieldApiName) {
        String memberId = param.getEvent().get(TransactionEventConstants.MEMBER_ID, String.class);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, ExtendedAttributeConstants.ATTR_API_NAME, fieldApiName);
        addExtFixFilter(param, objApiName, filters, Lists.newArrayList(param.getPolicy().getId()), memberId);
        List<IObjectData> dataList = getBaseData(filters, param.getUser().getTenantId(), objApiName);
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        return dataList.get(0);
    }

    public List<IObjectData> getExtendedAttributeList(LoyaltyExecParam param, String objApiName, List<String> fields, String memberId) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, ExtendedAttributeConstants.ATTR_API_NAME, fields);
        List<String> policyIds = param.getAllPolicy().stream().map(IncentivePolicy::getId).collect(Collectors.toList());
        addExtFixFilter(param, objApiName, filters, policyIds, memberId);
        return getBaseData(filters, param.getUser().getTenantId(), objApiName);
    }

    protected void addExtFixFilter(LoyaltyExecParam param, String objApiName, List<IFilter> filters, List<String> policyIds, String memberId) {
        if (StringUtils.isBlank(memberId)) {
            memberId = param.getEvent().get(ExtendedAttributeConstants.MEMBER_ID, String.class);
        }
        if (IncentiveMetricConstants.EXTENDED_ATTRIBUTE_MEMBER.equals(objApiName)) {
            SearchUtil.fillFilterEq(filters, ExtendedAttributeConstants.MEMBER_ID, memberId);
        } else if (IncentiveMetricConstants.EXTENDED_ATTRIBUTE_EVENT.equals(objApiName)) {
            SearchUtil.fillFilterEq(filters, ExtendedAttributeConstants.EVENT_ID, param.getEvent().getId());
        } else if (IncentiveMetricConstants.EXTENDED_ATTRIBUTE_POLICY.equals(objApiName)) {
            SearchUtil.fillFilterIn(filters, ExtendedAttributeConstants.POLICY_ID, policyIds);
        } else if (IncentiveMetricConstants.EXTENDED_ATTRIBUTE_MEMBER_INCENTIVE.equals(objApiName)) {
            SearchUtil.fillFilterEq(filters, ExtendedAttributeConstants.MEMBER_ID, memberId);
            SearchUtil.fillFilterIn(filters, ExtendedAttributeConstants.POLICY_ID, policyIds);
        }
    }

    public Object getRealValue(String valueType, Object value) {
        if (ExtendedAttributeConstants.DataType.TRUE_OR_FALSE.getDataType().equals(valueType)) {
            value = TypeUtils.castToBoolean(value);
        } else if (ExtendedAttributeConstants.DataType.DATE.getDataType().equals(valueType)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            try {
                cal.setTime(format.parse(value.toString()));
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            value = cal.getTimeInMillis();
        } else if (ExtendedAttributeConstants.DataType.NUMBER.getDataType().equals(valueType)) {
            value = TypeUtils.castToBigDecimal(value);
        }
        return value;
    }

    public Object getDbRealValue(String valueType, String valueKey, IObjectData att) {
        Object value = att.get(valueKey);
        if (ExtendedAttributeConstants.DataType.TRUE_OR_FALSE.getDataType().equals(valueType)) {
            value = att.get(valueKey, Boolean.class);
        } else if (ExtendedAttributeConstants.DataType.DATE.getDataType().equals(valueType)) {
            value = att.get(valueKey, Long.class);
        } else if (ExtendedAttributeConstants.DataType.NUMBER.getDataType().equals(valueType)) {
            value = att.get(valueKey, BigDecimal.class);
        }
        return value;
    }

    public String getMemberId(LoyaltyExecParam param, IncentivePolicyAction action) {
        String memberId = param.getEvent().get(TransactionEventConstants.MEMBER_ID, String.class);
        String beneficiary = action.getBeneficiary();
        if ("superior".equals(beneficiary)) {
            if (!param.isUpMemberQuery()) {
                memberId = memberQueryService.getParentIdByMemberId(param.getEvent().getTenantId(), memberId);
                param.setUpMemberQuery(true);
                param.setUpMemberId(memberId);
            }
            return param.getUpMemberId();
        }
        return memberId;
    }
}
