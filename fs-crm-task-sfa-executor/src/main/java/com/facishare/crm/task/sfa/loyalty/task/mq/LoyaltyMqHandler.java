package com.facishare.crm.task.sfa.loyalty.task.mq;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public abstract class LoyaltyMqHandler {

    static final Logger log = LoggerFactory.getLogger(LoyaltyMqHandler.class);

    @Resource
    ConfigService configService;

    public abstract Tag getTag();

    public abstract void handler(String body);

    public boolean enable(String tenantId) {
        return "true".equals(configService.findTenantConfig(User.systemUser(tenantId), "sfa_loyalty_init"));
    }

    public enum Tag {
        /**
         * 指定会员计算下次评定日
         */
        memberEvaluationDate,
        /**
         * 指定单个会员执行评定日
         */
        task,
        /**
         * 单个会员执行升级逻辑：
         * 发放优惠券
         */
        memberUpgrade,
        /**
         * 积分预警提醒
         */
        org,
        /**
         * 租户执行评定日
         */
        tenantMemberTask,
        /**
         * 租户计算下次评定日
         */
        tenantEvaluationDate,
        /**
         * 会员合并
         */
        memberMerge,
        /**
         * 评定日清空定级积分
         */
        delPointsDetailByEvaluationDate
    }
}
