package com.facishare.crm.task.sfa.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.impl.search.Where;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class RuleWhere implements Serializable {
    @JSONField(name = "filters")
    @JsonProperty("filters")
    private List<FiltersBean> filters;
    @JSONField(name = "connector")
    @JsonProperty("connector")
    private String connector;

    public RuleWhere(List<FiltersBean> filters) {
        this.connector = Where.CONN.OR.toString();
        this.filters = filters;
    }

    public RuleWhere() {
        this.connector = Where.CONN.OR.toString();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FiltersBean implements Serializable {
        @JSONField(name = "type")
        @JsonProperty("type")
        private String type;
        @JSONField(name = "operator")
        @JsonProperty("operator")
        private String operator;
        @JSONField(name = "field_name")
        @JsonProperty("field_name")
        private String fieldName;
        @JSONField(name = "field_name_pro")
        @JsonProperty("field_name_pro")
        /**
         * 高级单据条件，计算的结果值存放在此处
         */
        private BigDecimal fieldNamePro;
        @JSONField(name = "operator__s")
        @JsonProperty("operator__s")
        private String operatorS;
        @JSONField(name = "field_name__s")
        @JsonProperty("field_name__s")
        private String fieldNameS;
        @JSONField(name = "operator_name")
        @JsonProperty("operator_name")
        private String operatorName;
        @JSONField(name = "field_values__s")
        @JsonProperty("field_values__s")
        private String fieldValuesS;
        @JSONField(name = "is_master_field")
        @JsonProperty("is_master_field")
        private Boolean isMasterField;
        @JSONField(name = "field_values")
        @JsonProperty("field_values")
        private List<String> fieldValues;
        @JSONField(name = "field_name_type")
        @JsonProperty("field_name_type")
        private String fieldNameType;
        @JSONField(name = "object_api_name")
        @JsonProperty("object_api_name")
        private String objectApiName;
        @JSONField(name = "object_api_name_s")
        @JsonProperty("object_api_name_s")
        private String objectApiNameS;
        @JSONField(name = "field_value_type")
        @JsonProperty("field_value_type")
        private String fieldValueType;
        @JSONField(name = "agg_value_type")
        @JsonProperty("agg_value_type")
        private String aggValueType;
        @JSONField(name = "connector")
        @JsonProperty("connector")
        private String connector;
        @JSONField(name = "filter_type")
        @JsonProperty("filter_type")
        private String filterType;
        @JSONField(name = "return_type")
        @JsonProperty("return_type")
        private String returnType;
        @JSONField(name = "decimal_places")
        @JsonProperty("decimal_places")
        private Integer decimalPlaces;
        @JSONField(name = "default_to_zero")
        @JsonProperty("default_to_zero")
        private Boolean defaultToZero;
        @JSONField(name = "progressive_flag")
        @JsonProperty("progressive_flag")
        private Boolean progressiveFlag;
        @JSONField(name = "bind_object_api_name")
        @JsonProperty("bind_object_api_name")
        private String bindObjectApiName;
        @JSONField(name = "name_space")
        @JsonProperty("name_space")
        private String nameSpace;
        @JSONField(name = "apl_api_name")
        @JsonProperty("apl_api_name")
        private String APLApiName;
        @JSONField(name = "ext_api_name")
        @JsonProperty("ext_api_name")
        private String extApiName;
        @JSONField(name = "ext_default_value")
        @JsonProperty("ext_default_value")
        private String extDefaultValue;
        private boolean fromMetric;
        /**
         * 激励规则，field_name_type == metric时。记录其id
         */
        private String metricId;

        /**
         * 右值支持变量
         * 变量 variable
         * 常量 空或
         */
        @JSONField(name = "right_value_type")
        @JsonProperty("right_value_type")
        private String rightValueType;

        /**
         * 右值类型：
         * 普通字段 field
         * 查找关联 object_reference
         */
        @JSONField(name = "right_field_value_type")
        @JsonProperty("right_field_value_type")
        private String rightFieldValueType;

        /**
         * 右值类型：事件/指标
         * 事件：field
         * 指标：metric
         */
        @JSONField(name = "right_field_name_type")
        @JsonProperty("right_field_name_type")
        private String rightFieldNameType;
    }
}
