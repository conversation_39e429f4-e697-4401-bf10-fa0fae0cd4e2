package com.facishare.crm.task.sfa.services;

import com.facishare.crm.sfa.lto.duplicated.DuplicatedProcessingService;
import com.facishare.crm.sfa.lto.duplicated.models.DuplicatedModels;
import com.facishare.crm.sfa.lto.utils.GrayUtil;
import com.facishare.crm.sfa.lto.utils.ObjectDataUtil;
import com.facishare.crm.task.sfa.common.SfaTaskRateLimiterService;
import com.facishare.crm.task.sfa.common.constants.CommonConstant;
import com.facishare.crm.task.sfa.common.gray.GrayUtils;
import com.facishare.crm.task.sfa.common.util.CRMRecordUtil;
import com.facishare.crm.task.sfa.model.LeadsDuplicatedProcessingMessage;
import com.facishare.crm.task.sfa.util.LeadsUtil;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.facishare.crm.task.sfa.common.util.I18NKeyUtil.SFA_LEADS_DUP_DONE_TITLE;
import static com.facishare.crm.task.sfa.common.util.I18NKeyUtil.SFA_LEADS_Dup_Done;


@Service
@Slf4j
public class LeadsDuplicatedProcessingService {
    @Autowired
    private ICommonSqlService commonSqlService;

    @Autowired
    CRMNotificationServiceImpl crmNotificationService;

    @Autowired
    ObjectDataServiceImpl objectDataService;

    @Autowired
    private SfaTaskRateLimiterService sfaTaskRateLimiterService;

    @Autowired
    private SFALicenseService sfaLicenseService;
    @Autowired
    private GrayUtils grayUtils;
    @Autowired
    DuplicatedProcessingService duplicatedProcessingService;

    private Map<String,String> apiNameAndTableNameMap;

    private String LEADS_OBJ_API_NAME="LeadsObj";
    private String ACCOUNT_OBJ_API_NAME="AccountObj";
    private String CONTACT_OBJ_API_NAME="ContactObj";

    private int totalLimit=1000000;

    private static int sizeOfBatch=20;

    private static int errorLimit =100;

    static {
        ConfigFactory.getConfig("fs-crm-sales-config", config -> {
            sizeOfBatch = config.getInt("duplicated_process_size_of_batch", 20);
            errorLimit = config.getInt("duplicated_process_error_limit", 100);
        });
    }

    public void execute(LeadsDuplicatedProcessingMessage message) {
        String tenantId = message.getTenantId();
        if (!isGrayLeadsDuplicate(tenantId)) {
            return;
        }
        if (grayUtils.skipLeadsDuplicatedTenantId(tenantId)) {
            return;
        }
        User user = new User(String.valueOf(tenantId), CommonConstant.SUPER_USER);
        apiNameAndTableNameMap = Maps.newHashMap();
        apiNameAndTableNameMap.put(LEADS_OBJ_API_NAME, "biz_leads");
        apiNameAndTableNameMap.put(ACCOUNT_OBJ_API_NAME, "biz_account");
        apiNameAndTableNameMap.put(CONTACT_OBJ_API_NAME, "biz_contact");
        int requestVersion = message.getRefreshVersion();
        int refreshVersion = getRefreshVersion(tenantId);
        if (requestVersion < refreshVersion) {
            return; //多次更改规则，mq 积压后同时消费，只有最后一次修改的版本有效
        }
        List<String> dataIdList = message.getDataIdList();
        if (CollectionUtils.empty(dataIdList)) {
            String updater = message.getUpdater();
            refreshData(updater, user, refreshVersion);
        } else {
            //没有规则不需要处理
            if(!checkHasRule(tenantId)) {
                return;
            }
            refreshData(dataIdList, user, message.getSaveType(), refreshVersion);
        }
    }

    private void refreshData(List<String> dataIdList,User user, String triggerAction, int refreshVersion) {
        boolean skipTenant = grayUtils.skipLeadsDuplicatedTenantId(user.getTenantId());
        if (skipTenant) {
            log.info("refreshData skipLeadsDuplicatedTenantId:{}", user.getTenantId());
            return;
        }
        int failedNum = 0;
        List<List<String>> batches = splitList(dataIdList, sizeOfBatch);
        for (List<String> dataIds : batches) {
            if(skipTenant) {
                return;
            }
            if (CollectionUtils.notEmpty(dataIds)) {
                sfaTaskRateLimiterService.getLeadsDuplicatedProcessingLimiter().acquire();
                failedNum = doRefreshData(dataIds, failedNum, triggerAction, user, refreshVersion);
                //失败的请求次数过多说明处理重复数据代码有问题，停止执行
                if (failedNum > errorLimit){
                    log.error("stay of execution,failed response count over ranging");
                    break;
                }
            } else {
                break;
            }
        }
    }

    private  List<List<String>> splitList(List<String> list, int len) {
        List<List<String>> batches=Lists.newArrayList();
        if (CollectionUtils.empty(list) || len < 1)
        {
            return batches;
        }
        int size = list.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++)
        {
            int startIndex = i*len;
            int endIndex= ((i + 1) * len > size ? size : len * (i + 1));
            List <String> subList = list.subList(startIndex, endIndex);
            batches.add(subList);
        }
        return batches;
    }

    private void refreshData(String updater, User user, int refreshVersion) {
        boolean skipTenant = grayUtils.skipLeadsDuplicatedTenantId(user.getTenantId());
        if (skipTenant) {
            log.info("refreshData skipLeadsDuplicatedTenantId:{}", user.getTenantId());
            return;
        }
        if(skipLeadsDuplicatedRuleChangedTenantId(user.getTenantId())) {
            log.info("refreshData skipLeadsDuplicatedRuleChangedTenantId: {}", user.getTenantId());
            setIsRefreshed(user,refreshVersion);
            return;
        }
        try {
            int total = 0;
            int failedNum = 0;
            if (!setIsRefreshing(user, refreshVersion)) {  //该方法为同步方法，避免多线程调用时，线程1 线程2 都获取到数据库中没有记录，都执行插入的情况
                log.error("is_refreshing version not set,cant begin refresh");
                return;
            }
            boolean isSuccess = clearDuplicated(user.getTenantId(), LEADS_OBJ_API_NAME, refreshVersion);
            if (!isSuccess) return;

            long startTime = System.currentTimeMillis();
            log.info("start refresh,start time:{}", startTime);
            do {
                if (skipTenant) {
                    log.info("refreshData skipLeadsDuplicatedTenantId:{}", user.getTenantId());
                    return;
                }
                total++;
                int currentVersion = getRefreshVersion(user.getTenantId());
                if (currentVersion != refreshVersion) break; //当手动触发重新刷新时，重新从数据库中获取的version 和最初的version 会不一样，停止本次刷新。
                log.info("start get refresh data list,start time:{}", System.currentTimeMillis());
                List<String> objectDataIdList = getUnRefreshDataIdList(1000, user, refreshVersion);
                log.info("end get refresh data list,end time:{}, time interval:{} sec",
                        System.currentTimeMillis(), (System.currentTimeMillis() - startTime) / 1000);
                int count = objectDataIdList.size();
                if (count > 0) {
                    log.info("start do refresh data list,start time:{}", System.currentTimeMillis());
                    List<List<String>> splitList = splitList(objectDataIdList, sizeOfBatch);
                    for(List<String> idList : splitList) {
                        if (skipTenant) {
                            log.info("refreshData skipLeadsDuplicatedTenantId:{}", user.getTenantId());
                            return;
                        }
                        sfaTaskRateLimiterService.getLeadsDuplicatedProcessingLimiter().acquire();
                        failedNum = doRefreshData(idList, failedNum,"NONE",user, refreshVersion);
                    }
                    log.info("end do refresh data list,end time:{}, time interval:{} sec",
                            System.currentTimeMillis(), (System.currentTimeMillis() - startTime) / 1000);
                    if (failedNum > errorLimit) //失败的请求次数过多说明处理重复数据代码有问题，停止执行
                    {
                        log.error("stay of execution,failed response count over ranging");
                        throw new ValidateException("失败次数过多,停止执行");
                    }
                } else {
                    break;
                }
                log.info("leads duplicated refresh execute batch count:{} ,tenant_id {}", total, user.getTenantId());
                if (total == totalLimit) {
                    log.error("total is overrun");
                }
            }
            while (total <= totalLimit);
            long endTime = System.currentTimeMillis();
            double interval = (endTime - startTime) / 1000;
            log.info("end refresh,end time:{}, time interval:{} sec", endTime, interval);
            if (StringUtils.isNotEmpty(updater)) {
                sendMessage(updater, user);
            }
        } catch (Exception ex) {
            log.error("refresh duplicated error", ex);
            throw ex;
        } finally {
            setIsRefreshed(user,refreshVersion);
        }
    }

    private int doRefreshData(List<String> objectDataIdList,int failedNum,String triggerAction,User user, int refreshVersion) {
        try {
            String source = "TASK";
            if(!"NONE".equals(triggerAction)) {
                source = "TASK_" + triggerAction;
            }

            DuplicatedModels.ProcessArg arg = new DuplicatedModels.ProcessArg();
            arg.setObjectDataIdList(objectDataIdList);
            arg.setRefreshVersion(refreshVersion);
            arg.setSource(source);
            DuplicatedModels.ProcessResult result = duplicatedProcessingService.process(user, arg);
            String errorCode = result.getErrorCode();
            if (!"success".equals(errorCode)) {
                failedNum++;
            }
        } catch (Exception ex) {
            log.error("do refresh data rest invoke error", ex);
            failedNum++;
        }
        return failedNum;
    }

    private List<String> getUnRefreshDataIdList(int limit, User user, int refreshVersion) {
        List<String> result = Lists.newArrayList();
        if (user == null) return result;
        String querySql = String.format("select  id from  biz_leads " +
                "where tenant_id='%s' and (refresh_duplicated_version!=%s or refresh_duplicated_version is null) " +
                " and is_deleted=0 order by last_modified_time desc limit %s offset 0", user.getTenantId(),refreshVersion,limit);
        try {
            List<Map>  selected = objectDataService.findBySql(user.getTenantId(), querySql);
            if(CollectionUtils.notEmpty(selected)) {
                result =selected.stream().filter(m->StringUtils
                        .isNotEmpty(m.getOrDefault("id","").toString()))
                        .map(m->m.get("id").toString()).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("getUnRefreshDataIdList error {}", user.getTenantId(), e);
        }
        return result;
    }

    private boolean checkHasRule(String tenantId) {
        String querySql = String.format("SELECT id FROM biz_leads_duplicated_processing WHERE tenant_id='%s' AND is_deleted=0  LIMIT 1 ", tenantId);
        try {
            List<Map>  result = objectDataService.findBySql(tenantId, querySql);
            if(!CollectionUtils.empty(result)){
                return true;
            }
        } catch (Exception e) {
            log.error("checkHasRule error {}", tenantId, e);
        }
        return false;
    }

    private int getRefreshVersion(String tenantId) {
        int currentVersion=0;
        String querySql = String.format("select COALESCE(max(refresh_version),0) as refresh_version" +
                " from biz_leads_duplicated_processing where tenant_id='%s'", tenantId);
        try {
            List<Map>  result = objectDataService.findBySql(tenantId, querySql);
            if(!CollectionUtils.empty(result))
            {
                Map map=result.get(0);
                currentVersion=Integer
                        .valueOf(map.getOrDefault("refresh_version",0).toString());
            }
        } catch (Exception e) {
            log.error("getCurrentFreshVersion error {}", tenantId, e);
        }
        return currentVersion;
    }

    private boolean clearDuplicated( String tenantId,String objectApiName, int refreshVersion) {
        int limit = 100;
        boolean result = true;
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        ActionContext actionContext = LeadsUtil.createActionContext(user);
        int total = 0;
        long startTime = System.currentTimeMillis();
        log.info("start clear,start time:{}", startTime);
        try {
            do {
                if (grayUtils.skipLeadsDuplicatedTenantId(user.getTenantId())) {
                    log.info("refreshData skipLeadsDuplicatedTenantId", user.getTenantId());
                    return false;
                }
                total++;
                List<IObjectData> objectDataList = getDuplicatedObjectDataList(actionContext, objectApiName, limit, refreshVersion);
                int count = objectDataList.size();
                if (count > 0) {
                    updateDuplicatedFlag(user, objectDataList);
                    deleteDuplicatedRelation(actionContext, objectApiName, objectDataList, refreshVersion);
                } else {
                    break;
                }
                if (total > totalLimit) //TODO
                {
                    log.error("stay of execution");
                    result = false;
                }
            }
            while (total <= totalLimit);//避免死循环
        }
        catch (Exception ex)
        {
            log.error("clear duplicated fail",ex);
            result =false;
        }
        long endTime = System.currentTimeMillis();
        double interval = (endTime - startTime) / 1000;
        log.info("end clear,end time:{}, time interval:{} sec", endTime, interval);
        return result;
    }

    private void updateDuplicatedFlag(User user ,List<IObjectData> dataList) {
        dataList.stream().forEach(d->{
            d.set("is_duplicated",false);
            d.set("collected_to",null);
            d.set("is_collected", false);
            d.set("_id", d.get("id"));
        });

        try {
            ObjectDataUtil.updateFields(user, dataList,
                    Lists.newArrayList("is_duplicated", "collected_to", "is_collected"), false, true);
        } catch (Exception ex) {
            log.error("LeadsDuplicatedProcessingService.updateDuplicatedFlag error", ex);
        }
    }

    private void deleteDuplicatedRelation(ActionContext actionContext,String objectApiName, List<IObjectData> dataList, int refreshVersion)
    {
        List<String> dataIdList=dataList.stream().map(d->d.get("id",String.class)).collect(Collectors.toList());
        List<WhereParam> paramList=Lists.newArrayList();
        WhereParam param=new WhereParam();
        param.setColumn("tenant_id");
        param.setValue(Lists.newArrayList(actionContext.getEnterpriseId()));
        param.setOperator(CommonSqlOperator.EQ);
        paramList.add(param);
        param=new WhereParam();
        param.setColumn("object_api_name");
        param.setValue(Lists.newArrayList(objectApiName));
        param.setOperator(CommonSqlOperator.EQ);
        paramList.add(param);
        param=new WhereParam();
        param.setColumn("object_id");
        param.setValue(Lists.newArrayList(dataIdList));
        param.setOperator(CommonSqlOperator.IN);
        paramList.add(param);
        param = new WhereParam();
        param.setColumn("refresh_version");
        param.setOperator(CommonSqlOperator.NEQ);
        param.setValue(Lists.newArrayList(refreshVersion));
        paramList.add(param);
        try {
            commonSqlService.delete("biz_duplicated_processing_relation", paramList, actionContext);
        }
        catch (MetadataServiceException ex) {
            log.error("LeadsDuplicatedProcessingService.deleteDuplicatedRelation error", ex);
        }
    }

    private List<IObjectData> getDuplicatedObjectDataList(ActionContext actionContext,String objectApiName, int limit, int refreshVersion) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        String tenantId = actionContext.getEnterpriseId();
        try {
            CommonSqlSearchTemplate commonSqlSearchTemplate = new CommonSqlSearchTemplate();
            commonSqlSearchTemplate.setTableName(apiNameAndTableNameMap.get(objectApiName));
            commonSqlSearchTemplate.setOffset(0);
            commonSqlSearchTemplate.setLimit(limit);
            List<WhereParam> whereParamList = Lists.newArrayList();
            WhereParam whereParam = new WhereParam();
            whereParam.setColumn("tenant_id");
            whereParam.setOperator(CommonSqlOperator.valueOf("EQ"));
            whereParam.setValue(Lists.newArrayList(tenantId));
            whereParamList.add(whereParam);
            whereParam = new WhereParam();
            whereParam.setColumn("is_duplicated");
            whereParam.setOperator(CommonSqlOperator.EQ);
            whereParam.setValue(Lists.newArrayList(true));
            whereParamList.add(whereParam);
            whereParam = new WhereParam();
            whereParam.setColumn("refresh_duplicated_version");
            whereParam.setOperator(CommonSqlOperator.NEQ);
            whereParam.setValue(Lists.newArrayList(refreshVersion));
            whereParamList.add(whereParam);
            whereParam = new WhereParam();
            whereParam.setColumn("is_deleted");
            whereParam.setOperator(CommonSqlOperator.EQ);
            whereParam.setValue(Lists.newArrayList(0));
            whereParamList.add(whereParam);
            commonSqlSearchTemplate.setWhereParamList(whereParamList);
            List<Map> selectedMaps = commonSqlService.select(commonSqlSearchTemplate, actionContext);
            objectDataList = selectedMaps.stream()
                    .filter(m -> m.get("id") != null)
                    .map(m -> ObjectDataExt.of(m).getObjectData())
                    .collect(Collectors.toList());
        } catch (MetadataServiceException ex) {
            log.error("LeadsDuplicatedProcessingService.getDuplicatedObjectDataList error", ex);
        }
        return objectDataList;
    }

    private void sendMessage(String updater, User user ) {

        String content="历史线索的重复标记更新完毕，可查看最新结果";
        Set<Integer> receiverIds=Sets.newHashSet();
        if(NumberUtils.isNumber(updater)) {
            receiverIds.add(Integer.parseInt(updater));
        }
        if(CollectionUtils.empty(receiverIds)) return;
        CRMNotification crmNotification = CRMNotification.builder()
                .sender(user.getUserId())
                .remindRecordType(92)
                .content(content)
                .title("线索【重复】标记更新完毕")
                .dataId("")
                .content2Id("0")
                .receiverIds(receiverIds)
                .build();

        crmNotificationService.sendCRMNotification(user, crmNotification);

        //发送新crm通知
        CRMRecordUtil.sendNewCRMRecord(crmNotificationService, user,92,Lists.newArrayList(receiverIds),
                user.getUserId(),"线索【重复】标记更新完毕",content,SFA_LEADS_DUP_DONE_TITLE ,
                Lists.newArrayList(),SFA_LEADS_Dup_Done,Lists.newArrayList(),
                null);
    }

    private synchronized boolean setIsRefreshing(User user, int refreshVersion) {
        IActionContext actionContext = LeadsUtil.createActionContext(user);
        List<WhereParam> whereParams = getRefreshingWhereParam(user);
        int rows;
        try {
            List<Map> mapList = commonSqlService.select("biz_leads_duplicated_processing", whereParams, actionContext);
            if (CollectionUtils.empty(mapList)) {
                List<Map<String, Object>> insertMaps = Lists.newArrayList();
                Map<String, Object> insertMap = Maps.newHashMap();
                insertMap.put("id", UUID.randomUUID().toString().replace("-", ""));
                insertMap.put("tenant_id", actionContext.getEnterpriseId());
                insertMap.put("name", "is_in_refreshing");
                insertMap.put("description", "记录当前正在刷新的版本");
                insertMap.put("status", -1);
                insertMap.put("filters", "[]");
                insertMap.put("trigger_action", "TASK_REFRESH");
                insertMap.put("is_deleted", 1);
                insertMap.put("created_by", user.getUserId());
                insertMap.put("create_time", System.currentTimeMillis());
                insertMap.put("last_modified_by", user.getUserId());
                insertMap.put("last_modified_time", System.currentTimeMillis());
                insertMap.put("refresh_version", refreshVersion - 1);
                insertMaps.add(insertMap);
                rows = commonSqlService.insert("biz_leads_duplicated_processing", insertMaps, actionContext);
            } else {
                Map<String, Object> updateMap = Maps.newConcurrentMap();
                updateMap.put("refresh_version", refreshVersion - 1);
                rows = commonSqlService.update("biz_leads_duplicated_processing", updateMap, whereParams, actionContext);
            }
        } catch (MetadataServiceException ex) {
            log.error("save is_refreshing version error", ex);
            rows = -1;
        }
        if (rows != 1) {
            return false;
        }
        return true;
    }

    private List<WhereParam> getRefreshingWhereParam(User user)
    {
        List<WhereParam> whereParams = Lists.newArrayList();
        WhereParam whereParam = new WhereParam();
        whereParam.setColumn("tenant_id");
        whereParam.setOperator(CommonSqlOperator.EQ);
        whereParam.setValue(Lists.newArrayList(user.getTenantId()));
        whereParams.add(whereParam);
        whereParam = new WhereParam();
        whereParam.setColumn("name");
        whereParam.setOperator(CommonSqlOperator.EQ);
        whereParam.setValue(Lists.newArrayList("is_in_refreshing"));
        whereParams.add(whereParam);
        whereParam = new WhereParam();
        whereParam.setColumn("trigger_action");
        whereParam.setOperator(CommonSqlOperator.EQ);
        whereParam.setValue(Lists.newArrayList("TASK_REFRESH"));
        whereParams.add(whereParam);
        return whereParams;
    }

    private boolean setIsRefreshed(User user, int refreshVersion) {
        IActionContext actionContext = LeadsUtil.createActionContext(user);
        List<WhereParam> whereParams = Lists.newArrayList();
        //将该企业下的所有规则，设置成当前版本号，主要是置 is_in_refreshing ，否则会一直提示前端正在刷新。
        WhereParam whereParam = new WhereParam();
        whereParam.setColumn("tenant_id");
        whereParam.setOperator(CommonSqlOperator.EQ);
        whereParam.setValue(Lists.newArrayList(user.getTenantId()));
        whereParams.add(whereParam);
        int rows;
        try {
            log.info("begin update refresh_version");
            Map<String, Object> updateMap = Maps.newConcurrentMap();
            updateMap.put("refresh_version", refreshVersion);
            rows = commonSqlService.update("biz_leads_duplicated_processing", updateMap, whereParams, actionContext);
            log.info("end update refresh_version, version:{}, rows:{}",refreshVersion,rows);
        } catch (MetadataServiceException ex) {
            log.error("save is_refreshed version error", ex);
            rows = -1;
        }
        if (rows <= 0) {
            return false;
        }
        return true;
    }

    private boolean isGrayLeadsDuplicate(String tenantId) {
        return sfaLicenseService.checkModuleLicenseExist(tenantId, "leads_deduplication_app");
    }

    private boolean skipLeadsDuplicatedRuleChangedTenantId(String tenantId) {
        return GrayUtil.isGrayEnable("skip_leads_duplicated_rule_changed_tenant_id", tenantId);
    }
}