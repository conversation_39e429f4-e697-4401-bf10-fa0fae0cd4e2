package com.facishare.crm.task.sfa.services;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.task.sfa.common.util.CRMRecordUtil;
import com.facishare.crm.task.sfa.enums.ConfigType;
import com.facishare.crm.task.sfa.services.rebate.dao.mongo.RebateTaskInitDao;
import com.facishare.crm.task.sfa.services.rebate.dao.mongo.RebateTaskInitDocument;
import com.facishare.crm.task.sfa.services.rebate.util.DataOrganizationUtils;
import com.facishare.crm.task.sfa.services.rebate.util.MarketingInitUtil;
import com.facishare.crm.task.sfa.util.SFAConfigUtil;
import com.facishare.paas.appframework.common.mq.RocketMQMessageSender;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IFieldService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 抽象开启返利单服务
 */
@Slf4j
public abstract class AbsOpenRebateService {
    @Autowired
    protected EnterpriseInitService enterpriseInitService;
    @Autowired
    protected DescribeLogicService describeLogicService;
    @Autowired
    protected IFieldService fieldService;
    @Autowired
    protected SFABizObjMappingRuleWrapperService mappingRuleWrapperService;
    @Autowired
    protected ConfigService configService;
    @Autowired
    private CrmMenuInitService crmMenuInitService;
    @Autowired
    private UpdateAggregateFieldService updateAggregateFieldService;
    @Autowired
    private AddFundAccountService addFundAccountService;
    @Autowired
    protected CRMNotificationServiceImpl crmNotificationService;
    @Autowired
    protected RebateTaskInitDao rebateTaskInitDao;

    public static final String REBATE_RULE_OBJ = "RebateRuleObj";
    public static final String REBATE_POLICY_OBJ = "RebatePolicyObj";
    public static final String REBATE_POLICY_RULE_OBJ = "RebatePolicyRuleObj";
    public static final String REBATE_POLICY_LOG_OBJ = "RebatePolicyLogObj";
    public static final String REBATE_OBJ = "RebateObj";
    public static final String REBATE_DETAIL_OBJ = "RebateDetailObj";
    public static final String AMORTIZE_INFO_OBJ = "AmortizeInfoObj";
    public static final String AGGREGATE_RULE_OBJ = "AggregateRuleObj";

    /**
     * 价目表小计 = 价目表价格 * 数量
     * 价目表价格 =价目表售价 * 价目表折扣
     */
    private static final String DETAIL_PRICE_BOOK_SUBTOTAL_FORMULA = "$price_book_price$*$quantity$";
    /**
     * 整单折扣 = 销售订单金额/产品合计
     */
    private static final String MASTER_DISCOUNT_FORMULA = "$order_amount$/$product_amount$";
    /**
     * 销售订单金额 = 产品合计 + 额外调整
     */
    private static final String MASTER_ORDER_AMOUNT_FORMULA = "$product_amount$+$dynamic_amount$";

    private static final String DYNAMIC_PRICE_DEFAULT = "$amortize_subtotal$/$quantity$";
    /**
     * 仅添加一次金额字段
     */
    public static final ImmutableList<String[]> DETAIL_ONLY_ONCE_CURRENCY_FIELD_LIST = ImmutableList.of(
            new String[]{"price_book_subtotal", "价目表小计", DETAIL_PRICE_BOOK_SUBTOTAL_FORMULA},
            new String[]{"dynamic_amortize_amount", "整单调整分摊", "0"},
            new String[]{"amortize_price", "费用分摊后单价", DYNAMIC_PRICE_DEFAULT}
    );
    /**
     * 仅添加一次长文本字段，从对象和主对象都需要添加
     */
    public static final ImmutableList<String[]> ONLY_ONCE_LONG_TEXT_FIELD_LIST = ImmutableList.of(
            new String[]{"misc_content", "扩展字段"}
    );
    /**
     * 更新的字段
     */
    private static final ImmutableList<String[]> MASTER_MODIFY_CURRENCY_FIELD_LIST = ImmutableList.of(
            new String[]{"receivable_amount", "待回款金额（元）", null},
            new String[]{"discount", "整单折扣", MASTER_DISCOUNT_FORMULA},
            new String[]{"order_amount", "销售订单金额", MASTER_ORDER_AMOUNT_FORMULA}
    );

    public void openRebate(String tenantId, String userId) {
        try {
            initModule(tenantId, userId);
        } catch (Exception e) {
            log.error("开启返利单失败，失败消息{}", e.getMessage());
            setRebateConfigValue(User.systemUser(tenantId), "3");
            sendCrmNotice(tenantId, userId, "返利单开启通知", "开启返利单失败，请联系管理员",
                    "sfa.rebate.open.,notice"/*返利单开启通知*/, "sfa.rebate.open.fail"/*开启返利单失败，请联系管理员*/);
        }
    }

    private void initModule(String tenantId, String userId) {
        RequestContext requestContext = RequestContext.builder().user(new User(tenantId, User.SUPPER_ADMIN_USER_ID)).build();
        RequestContextManager.setContext(requestContext);
        StopWatch stopWatch = StopWatch.create("begin open rebate");
        log.info("begin open rebate tenantId:{},userId:{}", tenantId, userId);
        User user = new User(tenantId, userId);
        MarketingInitUtil.deleteDynamicAmortizeField(user);
        List<String> apiName = getInitApiNames(tenantId);
        initRebateOrCouponPlanObj(user, apiName);
        stopWatch.lap("initRebateOrCouponPlanObj end");
        addField(user);
        stopWatch.lap("addField end");
        //多组织去掉聚合值和分摊明细
        List<String> removeFields = Lists.newArrayList(Utils.AGGREGATE_RULE_API_NAME, Utils.AMORTIZE_INFO_API_NAME);
        DataOrganizationUtils.addDataOrganizationField(tenantId, apiName.stream().filter(x -> !removeFields.contains(x)).collect(Collectors.toList()));
        stopWatch.lap("DataOrganizationUtils.addDataOrganizationField end");
        addFieldMapping(tenantId);
        stopWatch.lap("addFieldMapping end");
        resetPriceConfig(tenantId);
        stopWatch.lap("resetPriceConfig end");
        addMenuItem(tenantId, apiName);
        stopWatch.lap("addMenuItem end");
        updateAggregateFieldService.executeUpdateAggregateObj(tenantId);
        stopWatch.lap("updateAggregateFieldService.executeUpdateAggregateObj end");
        addFundAccountService.createAndAuthorizeCustomerAccount(tenantId);
        stopWatch.lap("addFundAccountService.createAndAuthorizeCustomerAccount end");
        setRebateConfigValue(User.systemUser(tenantId), "1");
        stopWatch.lap("setRebateConfigValue end");
        stopWatch.logSlow(5_000);
        MarketingInitUtil.closeDynamicAmortize(user);
        RebateTaskInitDocument taskInitDocument = new RebateTaskInitDocument();
        taskInitDocument.setTenantId(tenantId);
        rebateTaskInitDao.initTask(taskInitDocument);
        sendCrmNotice(tenantId, userId, "返利单开启通知", "开启返利单成功",
                "sfa.rebate.open.notice"/*返利单开启通知*/, "sfa.rebate.open.success"/*开启返利单成功*/);
        sendMqAfterInitModuleSuccess(tenantId);
    }

    protected void sendMqAfterInitModuleSuccess(String tenantId) {
        RocketMQMessageSender sender = SpringUtil.getContext()
                .getBean("initModuleCtrlMQSender", RocketMQMessageSender.class);
        Map<String, String> initMsgMap = Maps.newHashMap();
        initMsgMap.put("tenantId", tenantId);
        initMsgMap.put("moduleCode", "rebate");
        byte[] bytes = JSON.toJSONString(initMsgMap).getBytes(StandardCharsets.UTF_8);
        sender.sendMessage(bytes);
    }


    private void sendCrmNotice(String tenantId, String receiverId, String title, String content, String i18nKeyTitle, String i18nKeyContent) {
        Set<Integer> receiverIds = Sets.newHashSet();
        if (NumberUtils.isNumber(receiverId)) {
            receiverIds.add(Integer.parseInt(receiverId));
        } else {
            log.error("userid is not number,userid:{}", receiverId);
            return;
        }
        CRMNotification crmNotification = CRMNotification.builder()
                .sender(User.SUPPER_ADMIN_USER_ID)
                .remindRecordType(92)
                .content(content)
                .title(title)
                .dataId("")
                .content2Id("0")
                .receiverIds(receiverIds)
                .build();
        User superAdminUser = User.systemUser(tenantId);
        crmNotificationService.sendCRMNotification(superAdminUser, crmNotification);
        //发送新crm通知
        CRMRecordUtil.sendNewCRMRecord(crmNotificationService, superAdminUser, 92, Lists.newArrayList(receiverIds),
                User.SUPPER_ADMIN_USER_ID, title, content, i18nKeyTitle,
                Lists.newArrayList(), i18nKeyContent, Lists.newArrayList(),
                null);
    }

    private void setRebateConfigValue(User user, String value) {
        String queryRst = configService.findTenantConfig(user, ConfigType.REBATE.getKey());
        if (StringUtils.isBlank(queryRst)) {
            configService.createTenantConfig(user, ConfigType.REBATE.getKey(), value, ConfigValueType.STRING);
        } else {
            configService.updateTenantConfig(user, ConfigType.REBATE.getKey(), value, ConfigValueType.STRING);
        }
    }

    protected void addMenuItem(String tenantId, List<String> apiName) {
        crmMenuInitService.createMenuItem(new User(tenantId, User.SUPPER_ADMIN_USER_ID),
                apiName,
                "RefundObj");
    }

    protected void addField(User user) {
        StopWatch stopWatch = StopWatch.create("addField");
        List<String> apiNames = Lists.newArrayList(Utils.SALES_ORDER_API_NAME, Utils.SALES_ORDER_PRODUCT_API_NAME);
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(user.getTenantId(), apiNames);
        stopWatch.lap("describeLogicService.findObjects end");
        //先从对象后主对象
        addFieldsOrderProduct(user, describeMap.get(Utils.SALES_ORDER_PRODUCT_API_NAME));
        stopWatch.lap("addFieldsOrderProduct end");
        addFieldsOrder(user, describeMap.get(Utils.SALES_ORDER_API_NAME));
        stopWatch.lap("addFieldsOrder end");
        stopWatch.logSlow(2000);
    }

    private void resetPriceConfig(String tenantId) {
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        String configValue = configService.findTenantConfig(user, ConfigType.GET_PRICE_WHEN_COPY.getKey());
        if ("0".equals(configValue)) {
            configService.updateTenantConfig(user, ConfigType.GET_PRICE_WHEN_COPY.getKey(), "1", ConfigValueType.STRING);
        }
    }


    protected void addFieldsOrderProduct(User user, IObjectDescribe iObjectDescribe) {
        List<IFieldDescribe> toAddFieldList = Lists.newArrayList();
        List<IFieldDescribe> toUpdateFieldList = Lists.newArrayList();
        List<IFieldDescribe> toEnableFieldList = Lists.newArrayList();
        List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList = Lists.newArrayList();
        addDetailNewFields(toAddFieldList, fieldLayoutTupleList);
        addDetailOnlyOnceField(toAddFieldList, fieldLayoutTupleList, iObjectDescribe, toEnableFieldList, toUpdateFieldList);
        addOrModifyDetailFields(toAddFieldList, toUpdateFieldList, fieldLayoutTupleList, iObjectDescribe);
        modifyDetailFields(toUpdateFieldList, iObjectDescribe, user);
        boolean openPeriodic = openPeriodic(user);
        setPeriodicQuantityRule(openPeriodic, toAddFieldList);
        setPeriodicQuantityRule(openPeriodic, toUpdateFieldList);
        MarketingInitUtil.doUpdateDescribeAndLayout(user, iObjectDescribe, toAddFieldList, toUpdateFieldList, fieldLayoutTupleList);
        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(toEnableFieldList)) {
            try {
                fieldService.enableField(iObjectDescribe, toEnableFieldList);
            } catch (MetadataServiceException e) {
                log.error("enableField error,tenantId {} ,", iObjectDescribe.getTenantId(), e);
            }
        }
    }

    private void addFieldMapping(String tenantId) {
        Map<String, String> fieldMapping = Maps.newHashMap();
        fieldMapping.put("total_amount", "price_book_subtotal");
        try {
            mappingRuleWrapperService.addFieldMapping(tenantId, "rule_quotelinesobj2salesorderproduct__c", fieldMapping);
        } catch (MetadataServiceException e) {
            log.error("add field mapping error when init rebate msg is {}", e.getMessage());
        }
    }

    /**
     * 只添加一次的字段，优惠券和返利单哪个先开，先添加
     *
     * @param toAddFieldList       添加字段列表
     * @param fieldLayoutTupleList 字段布局元组列表
     * @param iObjectDescribe      对象描述
     * @param toEnableFieldList    启用field
     * @param toUpdateFieldList    更新字段列表
     */
    protected void addDetailOnlyOnceField(List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList, IObjectDescribe iObjectDescribe, List<IFieldDescribe> toEnableFieldList, List<IFieldDescribe> toUpdateFieldList) {
        MarketingInitUtil.addCurrencyFieldOnlyOnce(DETAIL_ONLY_ONCE_CURRENCY_FIELD_LIST, toAddFieldList, fieldLayoutTupleList, iObjectDescribe);
        MarketingInitUtil.addLongTextFieldOnlyOnce(ONLY_ONCE_LONG_TEXT_FIELD_LIST, toAddFieldList, iObjectDescribe);
    }

    /**
     * 添加订单
     *
     * @param user            用户
     * @param iObjectDescribe 我对象描述
     */
    protected void addFieldsOrder(User user, IObjectDescribe iObjectDescribe) {
        List<IFieldDescribe> toAddFieldList = Lists.newArrayList();
        List<IFieldDescribe> toUpdateFieldList = Lists.newArrayList();
        List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList = Lists.newArrayList();
        boolean openPoint = "SalesOrderObj".equals(iObjectDescribe.getApiName())
                && "true".equals(SFAConfigUtil.getConfigValue(user.getTenantId(), ConfigType.LOYALTY_PLUGIN_SWITCH_SALES_ORDER.getKey(),User.SUPPER_ADMIN_USER_ID));
        addMasterNewFields(toAddFieldList, fieldLayoutTupleList);
        addMasterOnlyOnceField(toAddFieldList, iObjectDescribe, fieldLayoutTupleList, toUpdateFieldList);
        addMasterOrModifyDetailFields(toAddFieldList, toUpdateFieldList, fieldLayoutTupleList, iObjectDescribe,openPoint);
        modifyMasterDetailFields(toUpdateFieldList, iObjectDescribe, user);
        MarketingInitUtil.addPolicyAmountTotal(toAddFieldList, iObjectDescribe, () ->
                //开启了返利或者优惠券,判断是否开启价格政策，如果开启了则增加 “订单产品促销优惠额合计” 和 “促销后合计”
                SFAConfigUtil.isOpenPricePolicySalesOrder(user.getTenantId())
        );
        MarketingInitUtil.doUpdateDescribeAndLayout(user, iObjectDescribe, toAddFieldList, toUpdateFieldList, fieldLayoutTupleList);

    }

    protected void modifyMasterDetailFields(List<IFieldDescribe> toUpdateFieldList, IObjectDescribe iObjectDescribe, User user) {
        //待回款金额（元）
        MarketingInitUtil.modifyFormulaFields(MASTER_MODIFY_CURRENCY_FIELD_LIST.get(0), toUpdateFieldList, iObjectDescribe,
                expression -> {
                    if (expression.contains("+$loyalty_amount$")) {
                        expression = StringUtils.replace(expression, "+$loyalty_amount$", "");
                    }
                    if (expression.contains("-$faccount_amount$")) {
                        expression = StringUtils.replace(expression, "-$faccount_amount$", "");
                    }
                    //已经有已付金额了，不再添加
                    if (expression.contains("$paid_amount$")) {
                        return expression;
                    }
                    //，把【已回款金额(元)】换成【已付金额】，其他不变）
                    return expression.replace("$payment_amount$", "$paid_amount$");
                });
        //以下俩字段，开启价格政策后，则不再改变
        boolean isChanged = SFAConfigUtil.isOpenPricePolicySalesOrder(user.getTenantId());
        //整单折扣
        MarketingInitUtil.modifyCurrencyFields(isChanged, MASTER_MODIFY_CURRENCY_FIELD_LIST.get(1), toUpdateFieldList, iObjectDescribe,
                defaultValue -> defaultValue);
        //销售订单金额
        MarketingInitUtil.modifyCurrencyFields(isChanged, MASTER_MODIFY_CURRENCY_FIELD_LIST.get(2), toUpdateFieldList, iObjectDescribe,
                defaultValue -> defaultValue);
    }


    protected abstract void addMasterOrModifyDetailFields(List<IFieldDescribe> toAddFieldList, List<IFieldDescribe> toUpdateFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList, IObjectDescribe iObjectDescribe,boolean openPoint);

    protected abstract void addMasterNewFields(List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList);

    protected void addMasterOnlyOnceField(List<IFieldDescribe> toAddFieldList, IObjectDescribe iObjectDescribe, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList, List<IFieldDescribe> toUpdateFieldList) {
        MarketingInitUtil.addLongTextFieldOnlyOnce(ONLY_ONCE_LONG_TEXT_FIELD_LIST, toAddFieldList, iObjectDescribe);
    }


    public void initRebateOrCouponPlanObj(User user, List<String> apiNames) {
        StopWatch stopWatch = StopWatch.create("initRebateOrCouponPlanObj begin");
        String tenantId = user.getTenantId();
        if (CollectionUtils.isEmpty(apiNames)) {
            return;
        }
        for (String apiName : apiNames) {
            enterpriseInitService.initDescribeForTenant(user.getTenantId(), apiName);
        }
        stopWatch.lap("initDescribeForTenant");

        enterpriseInitService.initMultiLayoutForOneTenant(apiNames, tenantId);
        stopWatch.lap("initMultiLayoutForOneTenant");

        enterpriseInitService.initPrivilegeRelate(apiNames, user, null, null, null);
        stopWatch.lap("initPrivilegeRelate");

        if (apiNames.contains(Utils.AGGREGATE_RULE_API_NAME)) {
            enterpriseInitService.initLayoutRule(user, "AggregateRuleObj_layout_rule_type__c");
            stopWatch.lap("initLayoutRule");
        }
        stopWatch.logSlow(2000);
    }


    protected abstract List<String> getInitApiNames(String tenantId);

    protected abstract void modifyDetailFields(List<IFieldDescribe> toUpdateFieldList, IObjectDescribe iObjectDescribe, User user);

    protected abstract void addOrModifyDetailFields(List<IFieldDescribe> toAddFieldList, List<IFieldDescribe> toUpdateFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList, IObjectDescribe iObjectDescribe);


    protected abstract void addDetailNewFields(List<IFieldDescribe> toAddFieldList, List<Tuple<IFieldDescribe, FieldLayoutPojo>> fieldLayoutTupleList);

    protected abstract boolean openPeriodic(User user);

    public static void setPeriodicQuantityRule(boolean openPeriodic, List<IFieldDescribe> fieldList) {
        if (!openPeriodic || com.facishare.paas.appframework.common.util.CollectionUtils.empty(fieldList)) {
            return;
        }
        for (IFieldDescribe describe : fieldList) {
            String defaultValue = describe.getDefaultValue() == null ? "" : describe.getDefaultValue().toString();
            if (StringUtils.isNotBlank(defaultValue) && defaultValue.contains("$quantity$") && !defaultValue.contains("$pricing_period$")) {
                defaultValue = StringUtils.replace(defaultValue, "$quantity$", "($quantity$*$pricing_period$)");
                describe.setDefaultValue(defaultValue);
            }
        }
    }
}
