package com.facishare.crm.task.sfa.objchange.service.handler;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.loyalty.constants.LoyaltyConstants;
import com.facishare.crm.sfa.lto.loyalty.utils.LoyaltyThreshold;
import com.facishare.crm.task.sfa.objchange.model.ObjectChangeMessage;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
public class MemberHandler implements ObjectChangeHandler {

    @Resource
    ServiceFacade serviceFacade;

    private final String MemberObj = "MemberObj";
    private final List<String> updateFieldList = Lists.newArrayList("name", "avatar", "birthday", "gender");

    @Override
    public String getApiName() {
        return MemberObj;
    }

    @Override
    public void doService(ObjectChangeMessage msg, MessageExt messageExt) {
        TraceContext.get().setTraceId(UUID.randomUUID().toString().replace("-", ""));

        if (!"u".equals(msg.getOp())) {
            return;
        }
        String tenantId = msg.getTenantId();
        for (ObjectChangeMessage.Content content : msg.getBody()) {
            JSONObject afterTriggerData = content.getAfterTriggerData();
            if (updateFieldList.stream().noneMatch(afterTriggerData::containsKey)) {
                return;
            }
            handle(tenantId, content.getObjectId());
        }
    }

    public void handle(String tenantId, String dataId) {
        if (!Boolean.TRUE.equals(serviceFacade.isExistObjectByApiName(tenantId, LoyaltyConstants.LoyaltyMember.API_NAME))) {
            return;
        }
        IObjectData marketingMember = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), dataId, MemberObj);
        String phone = marketingMember.get("phone", String.class);
        if (StringUtils.isEmpty(phone)) {
            return;
        }
        Map<String, Object> updateFields = new HashMap<>();
        updateFields.put("member_name", marketingMember.get("name"));
        updateFields.put("avatar", marketingMember.get("avatar"));
        updateFields.put("birthday", marketingMember.get("birthday"));
        String gender = marketingMember.get("gender", String.class);
        if ("female".equals(gender)) {
            updateFields.put("gender", "Ms");
        } else {
            updateFields.put("gender", "Mr");
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(query.getFilters(), "phone", phone);
        query.setLimit(LoyaltyThreshold.getCommonMaxSize());
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(User.systemUser(tenantId), LoyaltyConstants.LoyaltyMember.API_NAME, query);
        if (queryResult != null && !CollectionUtils.isEmpty(queryResult.getData())) {
            serviceFacade.batchUpdateWithMap(User.systemUser(tenantId), queryResult.getData(), updateFields);
        }
    }
}
