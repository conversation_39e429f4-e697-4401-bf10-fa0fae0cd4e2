package com.facishare.crm.task.sfa.loyalty.task.mq;

import com.facishare.crm.task.sfa.executor.consumer.AbstractMqListener;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

@Slf4j
@Component
public class LoyaltyAsyncListener extends AbstractMqListener {

    @Resource
    List<LoyaltyMqHandler> handlerList;

    @Override
    public String configName() {
        return "fs-crm-task-sfa-mq.ini";
    }

    @Override
    public String sectionNames() {
        return "loyalty-member";
    }

    @Override
    public MessageListenerConcurrently handle() {
        return (messagetList, context) -> {
            if (!CollectionUtils.isEmpty(messagetList)) {
                for (MessageExt msg : messagetList) {
                    for (LoyaltyMqHandler handler : handlerList) {
                        try {
                            if (handler.getTag().name().equals(msg.getTags())) {
                                TraceContext.get().setTraceId(UUID.randomUUID().toString().replace("-", ""));
                                log.info("会员消费mq:{}", new String(msg.getBody()));
                                handler.handler(new String(msg.getBody()));
                            }
                        } catch (Exception e) {
                            log.error("会员mq消费异常", e);
                            throw e;
                        }
                    }
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
    }

}
