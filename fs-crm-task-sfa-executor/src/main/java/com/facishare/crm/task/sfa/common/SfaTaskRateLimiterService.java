package com.facishare.crm.task.sfa.common;

import com.github.autoconf.ConfigFactory;
import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 限速服务 class
 *
 * <AUTHOR>
 * @date 2019/9/12
 */
@Component
@Data
public class SfaTaskRateLimiterService {
    private double locationCalculationLimit;
    private double leadsAllocateAsyncRateLimit;
    private double leadsAllocateBaseRateLimit;
    private double leadsAllocateRateLimit;
    private double leadsAllocateBatchRateLimit;
    private double leadsAllocateNewRateLimit;
    private double leadsOverTimeRateLimit;
    private double todoCompensationRateLimit;
    private double leadsDuplicatedProcessingLimit;
    private double invoiceProcessingLimit;
    private double salesOrderProcessingLimiter;
    private double accountPathChangeLimit;
    private double leadsAllocateOverTimeLimiter;
    private double productFiledInitLimiter;
    private double licenseMQConsumeInitLimiter;
    private double paymentPlanTimeCalcLimit;
    private double accountToAddressLimit;
    private double accountToAddressForAccountIdLimit;
    private double prmUpdateObjectLimit;
    private double projectLimit;
    private double projectStageLimit;
    private double projectTaskLimit;
    private double availableRangeLimit;
    private double pricePolicyLimit;
    private double priceBookLimit;
    private double incentivePolicyLimit;
    private double masterDataAppLimit;
    private double procurementCalculateLimit;
    private double enterpriseRelationLimit;
    private double rebateCreateLimit;
    private double changeOwnerLimit;

    private RateLimiter locationCalculationRateLimiter;
    private RateLimiter todoCompensationRateLimiter;
    private RateLimiter leadsAllocateAsyncRateLimiter;
    private RateLimiter leadsAllocateBaseRateLimiter;
    private RateLimiter leadsAllocateRateLimiter;
    private RateLimiter leadsAllocateBatchRateLimiter;
    private RateLimiter leadsAllocateNewRateLimiter;
    private RateLimiter leadsOverTimeRateLimiter;
    private RateLimiter leadsDuplicatedProcessingLimiter;
    private RateLimiter invoiceProcessingLimiter;
    private RateLimiter salesOrderTransferProcessingLimiter;
    private RateLimiter accountPathChangeRateLimiter;
    private RateLimiter leadsAllocateOverTimeRateLimiter;
    private RateLimiter productFiledInitRateLimiter;
    private RateLimiter licenseMQConsumeInitRateLimiter;
    private RateLimiter paymentPlanTimeCalcLimiter;
    private RateLimiter accountToAddressLimiter;
    private RateLimiter accountToAddressForAccountIdLimiter;
    private RateLimiter prmUpdateObjectLimiter;
    private RateLimiter projectLimiter;
    private RateLimiter projectStageLimiter;
    private RateLimiter projectTaskLimiter;

    private double marketingAttributionLimit;
    private RateLimiter marketingAttributionLimiter;
    private RateLimiter availableRangeLimiter;
    private RateLimiter pricePolicyLimiter;
    private RateLimiter priceBookLimiter;
    private RateLimiter incentivePolicyLimiter;
    private RateLimiter masterDataAppLimiter;
    private RateLimiter procurementCalculateLimiter;
    private RateLimiter enterpriseRelationLimiter;
    private RateLimiter rebateCreateLimiter;
    private RateLimiter changeOwnerLimiter;

    private double leadsDuplicatedNormalMsgLimit;
    private RateLimiter leadsDuplicatedNormalMsgLimiter;

    private double leadsDuplicatedSlowMsgLimit;
    private RateLimiter leadsDuplicatedSlowMsgLimiter;

    private double qywxConversionChangeLimit;
    private RateLimiter qywxConversionChangeLimiter;

    private double qywxSessionShardLimit;
    private RateLimiter qywxSessionShardLimiter;

    private double wechatFriendSyncLimit;
    private RateLimiter wechatFriendSyncLimiter;

    private double syncCategoryLimit;
    private RateLimiter syncCategoryLimiter;

    private double updateCategoryHasChildren;
    private RateLimiter updateCategoryHasChildrenLimiter;

    private double pivotTableHistoryLimit;
    private RateLimiter pivotTableHistoryLimiter;

    private double activitySummaryLimit;
    private RateLimiter activitySummaryLimiter;

    private double operationsTaskLimit;
    private RateLimiter operationsTaskLimiter;

    private double loyaltyTaskLimit;
    private RateLimiter loyaltyTaskLimiter;

    private double loyaltyMemberTaskLimit;
    private RateLimiter loyaltyMemberTaskLimiter;

    private double accountChangeHandlerTaskLimit;
    private RateLimiter accountChangeHandlerTaskLimiter;

    private double newOpportunityChangeHandlerLimit;
    private RateLimiter newOpportunityChangeHandlerLimiter;

    private double qywxQueryInterfaceLimit;
    private RateLimiter qywxQueryInterfaceLimiter;

    private double riskBrainLimit;
    private RateLimiter riskBrainLimiter;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-crm-task-sfa-rate-limit", config -> {
            locationCalculationLimit = config.getDouble("locationCalculationLimit", 20);
            leadsAllocateAsyncRateLimit = config.getDouble("leadsAllocateAsyncRateLimit", 20);
            leadsAllocateBaseRateLimit = config.getDouble("leadsAllocateBaseRateLimit", 20);
            leadsAllocateRateLimit = config.getDouble("leadsAllocateLimit", 20);
            leadsAllocateBatchRateLimit = config.getDouble("leadsAllocateBatchLimit", 20);
            leadsAllocateNewRateLimit = config.getDouble("leadsAllocateNewRateLimit", 20);
            leadsOverTimeRateLimit = config.getDouble("leadsOverTimeLimit", 20);
            todoCompensationRateLimit = config.getDouble("todoCompensationRateLimit", 20);
            leadsDuplicatedProcessingLimit = config.getDouble("leadsDuplicatedProcessingLimit", 20);
            invoiceProcessingLimit = config.getDouble("invoiceDataTransferProcessingLimit", 20);
            salesOrderProcessingLimiter = config.getDouble("salesOrderDataTransferProcessingLimiter", 40);
            accountPathChangeLimit = config.getDouble("accountPathChangeLimit", 20);
            leadsAllocateOverTimeLimiter = config.getDouble("leadsAllocateOverTimeLimiter", 20);
            productFiledInitLimiter = config.getDouble("productFiledInitLimit", 20);
            licenseMQConsumeInitLimiter = config.getDouble("licenseMQConsumeInitLimiter", 20);
            paymentPlanTimeCalcLimit = config.getDouble("paymentPlanTimeCalcLimit", 30);
            accountToAddressLimit = config.getDouble("accountToAddressLimit", 20);
            accountToAddressForAccountIdLimit = config.getDouble("accountToAddressForAccountIdLimit", 500);
            prmUpdateObjectLimit = config.getDouble("prmUpdateObjectLimit", 10);
            projectLimit = config.getDouble("projectLimit", 30);
            projectStageLimit = config.getDouble("projectStageLimit", 30);
            projectTaskLimit = config.getDouble("projectTaskLimit", 60);
            marketingAttributionLimit = config.getDouble("marketingAttributionLimit", 20);
            availableRangeLimit = config.getDouble("availableRangeLimit", 500);
            pricePolicyLimit = config.getDouble("pricePolicyLimit", 500);
            priceBookLimit = config.getDouble("priceBookLimit", 500);
            incentivePolicyLimit = config.getDouble("incentivePolicyLimit", 500);
            masterDataAppLimit = config.getDouble("masterDataAppLimit", 50);
            procurementCalculateLimit = config.getDouble("procurementCalculateLimit", 10);
            enterpriseRelationLimit = config.getDouble("enterpriseRelationLimit", 20);
            rebateCreateLimit = config.getDouble("rebateCreateLimit", 100);
            leadsDuplicatedNormalMsgLimit = config.getDouble("leadsDuplicatedNormalMsgLimit", 20);
            leadsDuplicatedSlowMsgLimit = config.getDouble("leadsDuplicatedSlowMsgLimit", 1);
            changeOwnerLimit = config.getDouble("changeOwnerLimit", 500);
            qywxConversionChangeLimit = config.getDouble("qywxConversionChangeLimit", 20);
            qywxSessionShardLimit = config.getDouble("qywxSessionShardLimit", 20);
            syncCategoryLimit = config.getDouble("syncCategoryLimit", 500);
            updateCategoryHasChildren = config.getDouble("updateCategoryHasChildren", 500);
            wechatFriendSyncLimit = config.getDouble("wechatFriendSyncLimit", 50);
            pivotTableHistoryLimit = config.getDouble("pivotTableHistoryLimit", 0.005D);
            activitySummaryLimit = config.getDouble("activitySummaryLimit", 10D);
            operationsTaskLimit = config.getDouble("operationsTaskLimit", 20);
            loyaltyTaskLimit = config.getDouble("loyaltyTaskLimit", 100);
            loyaltyMemberTaskLimit = config.getDouble("loyaltyMemberTaskLimit", 50);
            accountChangeHandlerTaskLimit = config.getDouble("accountChangeHandlerTaskLimit", 50);
            newOpportunityChangeHandlerLimit = config.getDouble("newOpportunityChangeHandlerLimit", 50);
            qywxQueryInterfaceLimit = config.getDouble("qywxQueryInterfaceLimit", 10);

            locationCalculationRateLimiter = RateLimiter.create(locationCalculationLimit);
            leadsAllocateAsyncRateLimiter = RateLimiter.create(leadsAllocateAsyncRateLimit);
            leadsAllocateBaseRateLimiter = RateLimiter.create(leadsAllocateBaseRateLimit);
            leadsAllocateRateLimiter = RateLimiter.create(leadsAllocateRateLimit);
            leadsAllocateBatchRateLimiter = RateLimiter.create(leadsAllocateBatchRateLimit);
            leadsAllocateNewRateLimiter = RateLimiter.create(leadsAllocateNewRateLimit);
            leadsOverTimeRateLimiter = RateLimiter.create(leadsOverTimeRateLimit);
            todoCompensationRateLimiter = RateLimiter.create(todoCompensationRateLimit);
            if (leadsDuplicatedProcessingLimiter == null) {
                leadsDuplicatedProcessingLimiter = RateLimiter.create(leadsDuplicatedProcessingLimit);
            } else {
                leadsDuplicatedProcessingLimiter.setRate(leadsDuplicatedProcessingLimit);
            }

            if (leadsDuplicatedNormalMsgLimiter == null) {
                leadsDuplicatedNormalMsgLimiter = RateLimiter.create(leadsDuplicatedNormalMsgLimit);
            } else {
                leadsDuplicatedNormalMsgLimiter.setRate(leadsDuplicatedNormalMsgLimit);
            }

            if (leadsDuplicatedSlowMsgLimiter == null) {
                leadsDuplicatedSlowMsgLimiter = RateLimiter.create(leadsDuplicatedSlowMsgLimit);
            } else {
                leadsDuplicatedSlowMsgLimiter.setRate(leadsDuplicatedSlowMsgLimit);
            }
            paymentPlanTimeCalcLimiter = RateLimiter.create(paymentPlanTimeCalcLimit);
            invoiceProcessingLimiter = RateLimiter.create(invoiceProcessingLimit);
            salesOrderTransferProcessingLimiter = RateLimiter.create(salesOrderProcessingLimiter);
            accountPathChangeRateLimiter = RateLimiter.create(accountPathChangeLimit);
            leadsAllocateOverTimeRateLimiter = RateLimiter.create(leadsAllocateOverTimeLimiter);
            productFiledInitRateLimiter = RateLimiter.create(productFiledInitLimiter);
            licenseMQConsumeInitRateLimiter = RateLimiter.create(licenseMQConsumeInitLimiter);
            accountToAddressLimiter = RateLimiter.create(accountToAddressLimit);
            accountToAddressForAccountIdLimiter = RateLimiter.create(accountToAddressForAccountIdLimit);
            prmUpdateObjectLimiter = RateLimiter.create(prmUpdateObjectLimit);
            projectLimiter = RateLimiter.create(projectLimit);
            projectStageLimiter = RateLimiter.create(projectStageLimit);
            projectTaskLimiter = RateLimiter.create(projectTaskLimit);
            marketingAttributionLimiter = RateLimiter.create(marketingAttributionLimit);
            availableRangeLimiter = RateLimiter.create(availableRangeLimit);
            pricePolicyLimiter = RateLimiter.create(pricePolicyLimit);
            priceBookLimiter = RateLimiter.create(priceBookLimit);
            incentivePolicyLimiter = RateLimiter.create(incentivePolicyLimit);
            masterDataAppLimiter = RateLimiter.create(masterDataAppLimit);
            procurementCalculateLimiter = RateLimiter.create(procurementCalculateLimit);
            enterpriseRelationLimiter = RateLimiter.create(enterpriseRelationLimit);
            rebateCreateLimiter = RateLimiter.create(rebateCreateLimit);
            changeOwnerLimiter = RateLimiter.create(changeOwnerLimit);
            qywxConversionChangeLimiter = RateLimiter.create(qywxConversionChangeLimit);
            wechatFriendSyncLimiter = RateLimiter.create(wechatFriendSyncLimit);
            qywxSessionShardLimiter = RateLimiter.create(qywxSessionShardLimit);
            syncCategoryLimiter = RateLimiter.create(syncCategoryLimit);
            updateCategoryHasChildrenLimiter = RateLimiter.create(updateCategoryHasChildren);
            pivotTableHistoryLimiter = RateLimiter.create(pivotTableHistoryLimit);
            activitySummaryLimiter = RateLimiter.create(activitySummaryLimit);
            operationsTaskLimiter = RateLimiter.create(operationsTaskLimit);
            loyaltyTaskLimiter = RateLimiter.create(loyaltyTaskLimit);
            loyaltyMemberTaskLimiter = RateLimiter.create(loyaltyMemberTaskLimit);
            accountChangeHandlerTaskLimiter = RateLimiter.create(accountChangeHandlerTaskLimit);
            newOpportunityChangeHandlerLimiter = RateLimiter.create(newOpportunityChangeHandlerLimit);
            qywxQueryInterfaceLimiter = RateLimiter.create(qywxQueryInterfaceLimit);

            riskBrainLimit = config.getDouble("riskBrainLimit", 10);
            riskBrainLimiter = RateLimiter.create(riskBrainLimit);
        });
    }
}
