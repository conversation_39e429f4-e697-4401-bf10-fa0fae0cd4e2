package com.facishare.crm.task.sfa.loyalty.task.mq;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.loyalty.model.Loyalty;
import com.facishare.crm.task.sfa.loyalty.service.LoyaltyPointsDetailClearService;
import com.github.trace.TraceContext;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
public class DelPointsDetailByEvaluationDateHandler extends LoyaltyMqHandler {

    @Resource
    LoyaltyPointsDetailClearService loyaltyPointsDetailClearService;

    @Override
    public Tag getTag() {
        return Tag.delPointsDetailByEvaluationDate;
    }

    @Override
    public void handler(String body) {
        String traceId = TraceContext.get().getTraceId();
        Loyalty.ClearPointsDetail clearPointsDetail = JSONObject.parseObject(body.getBytes(), Loyalty.ClearPointsDetail.class);
        if (StringUtils.isEmpty(clearPointsDetail.getTraceId())) {
            clearPointsDetail.setTraceId(traceId);
        } else {
            TraceContext.get().setTraceId(clearPointsDetail.getTraceId());
        }
        loyaltyPointsDetailClearService.clearPointsDetailForEvaluationDate(clearPointsDetail);
    }
}
